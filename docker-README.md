# Docker конфигурация для <PERSON> Clou Web

## Описание

Этот проект содержит Docker конфигурацию для запуска веб-версии Flutter приложения Tony <PERSON>lou.

## Файлы конфигурации

- `Dockerfile` - Multi-stage сборка приложения
- `docker-compose.yml` - Оркестрация контейнеров
- `nginx.conf` - Конфигурация веб-сервера
- `.dockerignore` - Исключения для Docker build

## Быстрый старт

### Продакшн версия

```bash
# Сборка и запуск продакшн версии
docker-compose up --build

# Или в фоновом режиме
docker-compose up -d --build
```

Приложение будет доступно по адресу: http://localhost:8080

### Версия для разработки

```bash
# Запуск версии для разработки с hot reload
docker-compose --profile dev up tony-clou-dev

# Или в фоновом режиме
docker-compose --profile dev up -d tony-clou-dev
```

Версия для разработки будет доступна по адресу: http://localhost:8081

## Команды управления

### Основные команды

```bash
# Сборка образа
docker-compose build

# Запуск контейнеров
docker-compose up

# Запуск в фоновом режиме
docker-compose up -d

# Остановка контейнеров
docker-compose down

# Просмотр логов
docker-compose logs -f

# Перезапуск сервиса
docker-compose restart tony-clou-web
```

### Управление данными

```bash
# Очистка всех данных
docker-compose down -v

# Пересборка без кэша
docker-compose build --no-cache

# Удаление неиспользуемых образов
docker system prune -a
```

## Структура проекта

```
.
├── Dockerfile              # Multi-stage сборка
├── docker-compose.yml      # Оркестрация
├── nginx.conf             # Конфигурация Nginx
├── .dockerignore          # Исключения для сборки
└── logs/                  # Логи Nginx (создается автоматически)
```

## Особенности конфигурации

### Dockerfile
- **Multi-stage build**: Сборка во Flutter контейнере, запуск в Nginx
- **Оптимизированный размер**: Финальный образ содержит только статические файлы
- **Безопасность**: Использование Alpine Linux

### Nginx
- **SPA поддержка**: Правильная обработка роутинга
- **Кэширование**: Оптимизированное кэширование статических ресурсов
- **Сжатие**: Gzip сжатие для улучшения производительности
- **Безопасность**: Заголовки безопасности

### Docker Compose
- **Продакшн сервис**: Оптимизированный для продакшена
- **Dev сервис**: С hot reload для разработки
- **Health checks**: Проверка состояния приложения
- **Сети**: Изолированная сеть для сервисов

## Мониторинг

### Health Check
Приложение включает endpoint для проверки состояния:
```bash
curl http://localhost:8080/health
```

### Логи
Логи Nginx сохраняются в директории `./logs/nginx/`:
```bash
# Просмотр логов доступа
tail -f logs/nginx/access.log

# Просмотр логов ошибок
tail -f logs/nginx/error.log
```

## Переменные окружения

Вы можете настроить следующие переменные в `docker-compose.yml`:

- `NGINX_HOST` - Хост для Nginx (по умолчанию: localhost)
- `NGINX_PORT` - Порт для Nginx (по умолчанию: 80)

## Производительность

### Оптимизации
- Статические файлы кэшируются на 1 год
- HTML файлы не кэшируются для обновлений
- Gzip сжатие для текстовых файлов
- Оптимизированная конфигурация Nginx

### Масштабирование
```bash
# Запуск нескольких экземпляров
docker-compose up --scale tony-clou-web=3
```

## Безопасность

- Заголовки безопасности (CSP, X-Frame-Options, etc.)
- Минимальный Alpine образ
- Отсутствие исходного кода в продакшн образе
- Изолированная сеть контейнеров

## Troubleshooting

### Проблемы со сборкой
```bash
# Очистка кэша Docker
docker builder prune

# Пересборка без кэша
docker-compose build --no-cache
```

### Проблемы с портами
```bash
# Проверка занятых портов
netstat -tulpn | grep :8080

# Изменение порта в docker-compose.yml
ports:
  - "8081:80"  # Изменить на свободный порт
```

### Проблемы с правами
```bash
# Создание директории для логов
mkdir -p logs/nginx
chmod 755 logs/nginx
```
