#!/bin/bash

# Скрипт управления Docker контейнерами для Tony Clou Web

set -e

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Функция для вывода сообщений
log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Функция помощи
show_help() {
    echo -e "${BLUE}Tony Clou Web Docker Management Script${NC}"
    echo ""
    echo "Использование: $0 [КОМАНДА]"
    echo ""
    echo "Команды:"
    echo "  build       - Собрать Docker образ"
    echo "  start       - Запустить продакшн версию"
    echo "  dev         - Запустить версию для разработки"
    echo "  stop        - Остановить все контейнеры"
    echo "  restart     - Перезапустить продакшн версию"
    echo "  logs        - Показать логи"
    echo "  clean       - Очистить все данные"
    echo "  status      - Показать статус контейнеров"
    echo "  health      - Проверить здоровье приложения"
    echo "  shell       - Войти в контейнер"
    echo "  help        - Показать эту справку"
    echo ""
}

# Проверка наличия Docker
check_docker() {
    if ! command -v docker &> /dev/null; then
        error "Docker не установлен!"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose не установлен!"
        exit 1
    fi
}

# Создание необходимых директорий
setup_directories() {
    log "Создание необходимых директорий..."
    mkdir -p logs/nginx
    chmod 755 logs/nginx
}

# Сборка образа
build() {
    log "Сборка Docker образа..."
    setup_directories
    docker-compose build --no-cache
    log "Сборка завершена!"
}

# Запуск продакшн версии
start() {
    log "Запуск продакшн версии..."
    setup_directories
    docker-compose up -d --build
    log "Приложение запущено на http://localhost:8080/web"
}

# Запуск версии для разработки
dev() {
    log "Запуск версии для разработки..."
    setup_directories
    docker-compose --profile dev up -d tony-clou-dev
    log "Версия для разработки запущена на http://localhost:8081"
}

# Остановка контейнеров
stop() {
    log "Остановка контейнеров..."
    docker-compose down
    docker-compose --profile dev down
    log "Контейнеры остановлены!"
}

# Перезапуск
restart() {
    log "Перезапуск продакшн версии..."
    docker-compose restart tony-clou-web
    log "Перезапуск завершен!"
}

# Просмотр логов
logs() {
    log "Показ логов (Ctrl+C для выхода)..."
    docker-compose logs -f
}

# Очистка
clean() {
    warn "Это удалит все контейнеры, образы и данные!"
    read -p "Продолжить? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log "Очистка данных..."
        docker-compose down -v
        docker-compose --profile dev down -v
        docker system prune -a -f
        log "Очистка завершена!"
    else
        log "Очистка отменена"
    fi
}

# Статус контейнеров
status() {
    log "Статус контейнеров:"
    docker-compose ps
    echo ""
    log "Статус dev контейнеров:"
    docker-compose --profile dev ps
}

# Проверка здоровья
health() {
    log "Проверка здоровья приложения..."
    
    # Проверка продакшн версии
    if curl -f -s http://localhost:8080/health > /dev/null; then
        log "✅ Продакшн версия работает (http://localhost:8080/web)"
    else
        warn "❌ Продакшн версия недоступна"
    fi
    
    # Проверка dev версии
    if curl -f -s http://localhost:8081 > /dev/null; then
        log "✅ Dev версия работает (http://localhost:8081)"
    else
        warn "❌ Dev версия недоступна"
    fi
}

# Вход в контейнер
shell() {
    log "Вход в продакшн контейнер..."
    docker-compose exec tony-clou-web sh
}

# Основная логика
main() {
    check_docker
    
    case "${1:-help}" in
        build)
            build
            ;;
        start)
            start
            ;;
        dev)
            dev
            ;;
        stop)
            stop
            ;;
        restart)
            restart
            ;;
        logs)
            logs
            ;;
        clean)
            clean
            ;;
        status)
            status
            ;;
        health)
            health
            ;;
        shell)
            shell
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            error "Неизвестная команда: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Запуск скрипта
main "$@"
