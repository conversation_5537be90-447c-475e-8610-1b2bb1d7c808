#!/bin/bash

# Скрипт развертывания Tony Clou на prolesfero.space

set -e

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Функции для вывода
log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Проверка окружения
check_environment() {
    log "Проверка окружения для развертывания на prolesfero.space..."
    
    # Проверка Docker
    if ! command -v docker &> /dev/null; then
        error "Docker не установлен!"
        exit 1
    fi
    
    # Проверка Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose не установлен!"
        exit 1
    fi
    
    # Проверка прав на порты 80 и 443
    if [ "$EUID" -ne 0 ] && ! docker ps &> /dev/null; then
        warn "Для использования портов 80 и 443 может потребоваться sudo"
    fi
    
    log "✅ Окружение готово"
}

# Настройка SSL сертификатов
setup_ssl() {
    log "Настройка SSL сертификатов для prolesfero.space..."
    
    echo "Выберите тип SSL сертификата:"
    echo "1) Let's Encrypt (рекомендуется для продакшена)"
    echo "2) Самоподписанный (для тестирования)"
    
    read -p "Ваш выбор (1-2): " ssl_choice
    
    case $ssl_choice in
        1)
            setup_letsencrypt
            ;;
        2)
            setup_self_signed
            ;;
        *)
            error "Неверный выбор"
            exit 1
            ;;
    esac
}

# Настройка Let's Encrypt
setup_letsencrypt() {
    log "Настройка Let's Encrypt сертификата..."
    
    read -p "Введите ваш email для Let's Encrypt: " email
    
    if [ -z "$email" ]; then
        error "Email обязателен для Let's Encrypt"
        exit 1
    fi
    
    log "Создание Let's Encrypt сертификата для prolesfero.space..."
    
    # Остановка контейнеров для освобождения портов
    docker-compose -f docker-compose.prod.yml down 2>/dev/null || true
    
    # Создание сертификата
    ./generate-ssl.sh letsencrypt prolesfero.space "$email"
    
    log "✅ Let's Encrypt сертификат создан"
}

# Настройка самоподписанного сертификата
setup_self_signed() {
    log "Создание самоподписанного сертификата для prolesfero.space..."
    ./generate-ssl.sh self-signed prolesfero.space
    log "✅ Самоподписанный сертификат создан"
}

# Развертывание приложения
deploy_application() {
    log "Развертывание Tony Clou на prolesfero.space..."
    
    # Создание необходимых директорий
    mkdir -p logs/nginx ssl
    
    # Сборка и запуск
    log "Сборка и запуск контейнеров..."
    docker-compose -f docker-compose.prod.yml up -d --build
    
    # Ожидание запуска
    log "Ожидание запуска приложения..."
    sleep 10
    
    # Проверка статуса
    if docker-compose -f docker-compose.prod.yml ps | grep -q "Up"; then
        log "✅ Приложение успешно развернуто!"
        log ""
        log "🌐 Ваше приложение доступно по адресам:"
        log "   HTTP:  http://prolesfero.space/web"
        log "   HTTPS: https://prolesfero.space/web"
        log ""
        log "📋 Полезные команды:"
        log "   make logs-prod     - Просмотр логов"
        log "   make health-prod   - Проверка здоровья"
        log "   make stop-prod     - Остановка"
        log "   make ssl-check     - Проверка SSL"
    else
        error "❌ Ошибка при развертывании!"
        log "Проверьте логи: docker-compose -f docker-compose.prod.yml logs"
        exit 1
    fi
}

# Проверка здоровья после развертывания
health_check() {
    log "Проверка здоровья приложения..."
    
    # Проверка HTTP
    if curl -s -o /dev/null -w "%{http_code}" http://prolesfero.space/health | grep -q "200"; then
        log "✅ HTTP работает"
    else
        warn "❌ HTTP недоступен"
    fi
    
    # Проверка HTTPS
    if curl -s -k -o /dev/null -w "%{http_code}" https://prolesfero.space/health | grep -q "200"; then
        log "✅ HTTPS работает"
    else
        warn "❌ HTTPS недоступен"
    fi
}

# Показать справку
show_help() {
    echo -e "${BLUE}Tony Clou Deployment Script для prolesfero.space${NC}"
    echo ""
    echo "Использование: $0 [КОМАНДА]"
    echo ""
    echo "Команды:"
    echo "  deploy    - Полное развертывание (по умолчанию)"
    echo "  ssl-only  - Только настройка SSL"
    echo "  check     - Проверка здоровья"
    echo "  help      - Показать эту справку"
    echo ""
    echo "Примеры:"
    echo "  $0                 # Полное развертывание"
    echo "  $0 deploy          # Полное развертывание"
    echo "  $0 ssl-only        # Только SSL"
    echo "  $0 check           # Проверка"
    echo ""
}

# Основная функция
main() {
    case "${1:-deploy}" in
        deploy)
            log "🚀 Начало развертывания Tony Clou на prolesfero.space"
            check_environment
            setup_ssl
            deploy_application
            health_check
            log "🎉 Развертывание завершено!"
            ;;
        ssl-only)
            log "🔒 Настройка SSL для prolesfero.space"
            setup_ssl
            log "✅ SSL настроен!"
            ;;
        check)
            health_check
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            error "Неизвестная команда: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Запуск скрипта
main "$@"
