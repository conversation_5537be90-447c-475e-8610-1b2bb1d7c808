# Исправление проблем CSP и роутинга для Flutter Web

## 🐛 Проблемы, которые были исправлены

### 1. Content Security Policy (CSP) ошибки
**Проблема:**
```
Refused to load the script 'https://www.gstatic.com/flutter-canvaskit/...' because it violates the following Content Security Policy directive
```

**Решение:**
Обновлен CSP заголовок в nginx конфигурации для разрешения загрузки Flutter ресурсов:

```nginx
add_header Content-Security-Policy "
  default-src 'self' https: data: blob: 'unsafe-inline' 'unsafe-eval'; 
  script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com; 
  connect-src 'self' https: wss: ws:; 
  img-src 'self' data: https:; 
  font-src 'self' data: https:; 
  style-src 'self' 'unsafe-inline' https:; 
  worker-src 'self' blob:; 
  child-src 'self' blob:
" always;
```

### 2. 404 ошибки для статических файлов
**Проблема:**
```
Failed to load resource: the server responded with a status of 404 (Not Found)
web/favicon.png:1
web/icons/Icon-192.png:1
```

**Решение:**
Исправлена nginx конфигурация для корректной обработки статических файлов:

```nginx
location /web/ {
    alias /usr/share/nginx/html/;
    try_files $uri @web_fallback;
    
    # Кэширование статических файлов
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

location @web_fallback {
    try_files /index.html =404;
}
```

### 3. Manifest.json ошибки
**Проблема:**
```
Manifest: property 'theme_color' ignored, '#hexcode' is not a valid color.
```

**Решение:**
Обновлен `web/manifest.json` с корректными цветами:

```json
{
    "background_color": "#ffffff",
    "theme_color": "#2196F3"
}
```

## ✅ Что было исправлено

### Файлы nginx конфигурации:
- ✅ `nginx.conf` - Обновлен CSP и роутинг
- ✅ `nginx.prod.conf` - Обновлен CSP и роутинг для продакшена

### Flutter конфигурация:
- ✅ `web/manifest.json` - Исправлены цвета
- ✅ Dockerfile - Корректная сборка с `--base-href /web/`

### Docker конфигурация:
- ✅ `docker-compose.yml` - Убрано устаревшее поле version
- ✅ `docker-compose.prod.yml` - Убрано устаревшее поле version

## 🔧 Технические детали исправлений

### CSP директивы:
- `script-src` - Разрешает загрузку скриптов с www.gstatic.com (Flutter CanvasKit)
- `connect-src` - Разрешает WebSocket и HTTPS соединения
- `worker-src` - Разрешает Web Workers для Flutter
- `child-src` - Разрешает iframe и child contexts

### Nginx роутинг:
- `alias` вместо `root` для корректного сопоставления путей
- `@web_fallback` для обработки SPA роутинга
- Корректная обработка статических файлов

## 🚀 Проверка исправлений

### 1. Запуск приложения
```bash
# Разработка (порт 8080)
make start

# Продакшн (порт 80)
make start-prod
```

### 2. Проверка в браузере
- Откройте http://localhost:8080/web (или http://localhost/web для продакшена)
- Проверьте консоль браузера - не должно быть CSP ошибок
- Проверьте Network tab - все ресурсы должны загружаться успешно

### 3. Проверка статических файлов
```bash
# Проверка favicon
curl -I http://localhost:8080/web/favicon.png

# Проверка иконок
curl -I http://localhost:8080/web/icons/Icon-192.png

# Проверка JavaScript
curl -I http://localhost:8080/web/flutter_bootstrap.js
```

## 📋 Чек-лист для проверки

- [ ] Приложение загружается без ошибок в консоли
- [ ] Flutter CanvasKit загружается с www.gstatic.com
- [ ] Статические файлы (favicon, иконки) загружаются
- [ ] SPA роутинг работает корректно
- [ ] Manifest.json не показывает ошибки цветов
- [ ] Приложение работает как на порту 8080, так и на порту 80

## 🔄 Если проблемы остаются

### Очистка кэша браузера:
```bash
# Chrome/Edge
Ctrl+Shift+R (Windows/Linux)
Cmd+Shift+R (macOS)

# Или через DevTools
F12 -> Network tab -> Disable cache
```

### Пересборка контейнеров:
```bash
# Полная очистка и пересборка
make clean
make start
```

### Проверка логов:
```bash
# Логи nginx
docker-compose logs -f

# Логи в файлах
tail -f logs/nginx/access.log
tail -f logs/nginx/error.log
```

Теперь ваше Flutter приложение должно работать корректно в Docker без CSP ошибок и проблем с загрузкой ресурсов! 🎉
