# Multi-stage build для Flutter Web приложения
FROM ghcr.io/cirruslabs/flutter:stable AS build

# Установка рабочей директории
WORKDIR /app

# Копирование файлов конфигурации
COPY pubspec.yaml pubspec.lock ./

# Установка зависимостей
RUN flutter pub get

# Копирование исходного кода
COPY . .

# Включение веб-поддержки Flutter
RUN flutter config --enable-web

# Сборка веб-приложения для продакшена
RUN flutter build web --release

# Production stage с nginx
FROM nginx:alpine

# Копирование собранного приложения
COPY --from=build /app/build/web /usr/share/nginx/html

# Копирование конфигурации nginx
COPY nginx.conf /etc/nginx/nginx.conf

# Создание директории для логов
RUN mkdir -p /var/log/nginx

# Открытие порта
EXPOSE 80

# Запуск nginx
CMD ["nginx", "-g", "daemon off;"]
