name: tony_clou_app
description: "<PERSON>"
publish_to: "none"
version: 0.1.7+2

environment:
  sdk: ">=3.7.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8

  # State management
  bloc: ^9.0.0
  flutter_bloc: ^9.1.1

  # Routing
  auto_route: ^10.0.1

  # Serialization
  freezed_annotation: ^3.0.0
  json_annotation: ^4.9.0

  # Storage
  shared_preferences: ^2.5.3
  flutter_secure_storage: ^9.2.4

  # Networking
  dio: ^5.8.0
  flutter_dotenv: ^5.2.1

  # Utilities
  logger: ^2.5.0
  package_info_plus: ^8.3.0
  flutter_svg: ^2.1.0
  intl: ^0.20.2
  screenshot: ^3.0.0
  share_plus: ^11.0.0
  permission_handler: ^12.0.0+1
  crypto: ^3.0.6
  url_launcher: ^6.3.1
  onesignal_flutter: ^5.3.3

  # QR
  qr: ^3.0.2
  qr_flutter: ^4.1.0
  mobile_scanner: ^7.0.0
  camera: ^0.11.1
  image_picker: ^1.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code generation
  build_runner: ^2.4.15
  freezed: ^3.0.6
  json_serializable: ^6.9.5
  auto_route_generator: ^10.0.1

  # Asset generation
  flutter_gen_runner: ^5.4.0
  flutter_gen: ^5.4.0
  flutter_launcher_icons: ^0.14.4
  change_app_package_name: ^1.5.0

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/logo.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    # image_path: "path/to/image.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    # image_path: "path/to/image.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    # image_path: "path/to/image.png"

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/icons/
    - assets/images/
    - assets/fonts/
    - .env

  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-VariableFont_opsz,wght.ttf
    - family: JetBrainsMono
      fonts:
        - asset: assets/fonts/JetBrainsMono.ttf

flutter_gen:
  output: lib/core/gen/
  line_length: 120
  integrations:
    flutter_svg: true
