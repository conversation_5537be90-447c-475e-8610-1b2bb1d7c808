# Настройка Tony Clou Web для работы на пути /web

## ✅ Выполненные изменения

### 1. Dockerfile
- Добавлен флаг `--base-href /web/` для сборки Flutter приложения
- Приложение теперь корректно работает с базовым путем `/web/`

```dockerfile
RUN flutter build web --release --base-href /web/
```

### 2. Nginx конфигурация
- Настроен автоматический редирект с корня `/` на `/web/`
- Настроена обработка SPA роутинга для пути `/web/`
- Оптимизировано кэширование статических файлов

```nginx
# Редирект с корня на /web
location = / {
    return 301 /web/;
}

# Основная конфигурация для SPA на /web
location /web/ {
    alias /usr/share/nginx/html/;
    try_files $uri $uri/ /web/index.html;
}
```

### 3. Обновлена документация
- `docker-README.md` - обновлены URL в примерах
- `Makefile` - обновлены команды и сообщения
- `docker-scripts.sh` - обновлены URL в проверках

## 🌐 Доступ к приложению

### Основные URL:
- **Приложение**: http://localhost:8080/web
- **Автоматический редирект**: http://localhost:8080 → http://localhost:8080/web
- **Health check**: http://localhost:8080/health

### Команды запуска:

```bash
# Быстрый запуск
docker-compose up -d --build

# Или через Makefile
make start

# Или через скрипт
./docker-scripts.sh start
```

## 🔧 Технические детали

### Flutter конфигурация
- `--base-href /web/` - устанавливает базовый путь для всех ресурсов
- Все ссылки в приложении автоматически получают префикс `/web/`
- Роутинг Flutter корректно работает с новым базовым путем

### Nginx конфигурация
- **Редирект 301**: Постоянное перенаправление с `/` на `/web/`
- **Alias директива**: Корректное сопоставление `/web/` с файлами в `/usr/share/nginx/html/`
- **SPA поддержка**: Все неизвестные пути перенаправляются на `/web/index.html`
- **Кэширование**: Статические файлы кэшируются на 1 год, HTML файлы не кэшируются

### Преимущества такой настройки:
1. **Гибкость**: Приложение может работать как часть большего сайта
2. **Совместимость**: Легко интегрируется с существующей инфраструктурой
3. **SEO**: Четкая структура URL для поисковых систем
4. **Безопасность**: Изолированное пространство имен для приложения

## 🚀 Проверка работоспособности

```bash
# Проверка основного приложения
curl -I http://localhost:8080/web

# Проверка редиректа
curl -I http://localhost:8080

# Проверка health check
curl http://localhost:8080/health

# Автоматическая проверка через скрипт
./docker-scripts.sh health
```

## 📝 Примечания

- Все внутренние ссылки Flutter приложения автоматически работают с новым базовым путем
- API запросы остаются без изменений (если они абсолютные)
- Статические ресурсы (изображения, шрифты, CSS, JS) корректно загружаются
- Роутинг приложения работает как обычно, но с префиксом `/web/`

## 🔄 Откат изменений

Если нужно вернуться к работе на корневом пути:

1. В `Dockerfile` убрать `--base-href /web/`:
```dockerfile
RUN flutter build web --release
```

2. В `nginx.conf` изменить конфигурацию:
```nginx
location / {
    try_files $uri $uri/ /index.html;
}
```

3. Пересобрать контейнер:
```bash
docker-compose up -d --build
```
