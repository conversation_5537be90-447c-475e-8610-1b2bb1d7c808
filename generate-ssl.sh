#!/bin/bash

# Скрипт для генерации SSL сертификатов для Tony Clou Web

set -e

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Функции для вывода
log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Создание директории для SSL
create_ssl_dir() {
    log "Создание директории для SSL сертификатов..."
    mkdir -p ssl
    chmod 755 ssl
}

# Генерация самоподписанного сертификата
generate_self_signed() {
    local domain=${1:-localhost}
    
    log "Генерация самоподписанного сертификата для домена: $domain"
    
    # Создание конфигурационного файла для OpenSSL
    cat > ssl/openssl.conf << EOF
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=RU
ST=Moscow
L=Moscow
O=TonyClou
CN=$domain

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = $domain
DNS.2 = *.$domain
DNS.3 = localhost
DNS.4 = *.localhost
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

    # Генерация приватного ключа
    openssl genrsa -out ssl/key.pem 2048
    
    # Генерация сертификата
    openssl req -new -x509 -key ssl/key.pem -out ssl/cert.pem -days 365 -config ssl/openssl.conf -extensions v3_req
    
    # Установка правильных прав доступа
    chmod 600 ssl/key.pem
    chmod 644 ssl/cert.pem
    
    log "✅ Самоподписанный сертификат создан успешно!"
    log "   Сертификат: ssl/cert.pem"
    log "   Ключ: ssl/key.pem"
    log "   Срок действия: 365 дней"
}

# Генерация сертификата Let's Encrypt (требует certbot)
generate_letsencrypt() {
    local domain=$1
    local email=$2
    
    if [ -z "$domain" ] || [ -z "$email" ]; then
        error "Для Let's Encrypt требуется домен и email"
        echo "Использование: $0 letsencrypt domain.com <EMAIL>"
        exit 1
    fi
    
    log "Генерация Let's Encrypt сертификата для домена: $domain"
    
    # Проверка наличия certbot
    if ! command -v certbot &> /dev/null; then
        error "certbot не установлен!"
        log "Установите certbot:"
        log "  Ubuntu/Debian: sudo apt install certbot"
        log "  CentOS/RHEL: sudo yum install certbot"
        log "  macOS: brew install certbot"
        exit 1
    fi
    
    # Остановка контейнеров для освобождения портов
    log "Остановка Docker контейнеров..."
    docker-compose -f docker-compose.prod.yml down 2>/dev/null || true
    
    # Генерация сертификата
    sudo certbot certonly --standalone \
        --email "$email" \
        --agree-tos \
        --no-eff-email \
        -d "$domain"
    
    # Копирование сертификатов
    sudo cp "/etc/letsencrypt/live/$domain/fullchain.pem" ssl/cert.pem
    sudo cp "/etc/letsencrypt/live/$domain/privkey.pem" ssl/key.pem
    
    # Установка правильных прав доступа
    sudo chown $USER:$USER ssl/cert.pem ssl/key.pem
    chmod 644 ssl/cert.pem
    chmod 600 ssl/key.pem
    
    log "✅ Let's Encrypt сертификат создан успешно!"
    log "   Сертификат: ssl/cert.pem"
    log "   Ключ: ssl/key.pem"
    log "   Домен: $domain"
}

# Проверка существующих сертификатов
check_certificates() {
    log "Проверка SSL сертификатов..."
    
    if [ -f "ssl/cert.pem" ] && [ -f "ssl/key.pem" ]; then
        log "✅ Сертификаты найдены:"
        
        # Информация о сертификате
        local subject=$(openssl x509 -in ssl/cert.pem -noout -subject 2>/dev/null | sed 's/subject=//')
        local issuer=$(openssl x509 -in ssl/cert.pem -noout -issuer 2>/dev/null | sed 's/issuer=//')
        local not_after=$(openssl x509 -in ssl/cert.pem -noout -dates 2>/dev/null | grep notAfter | sed 's/notAfter=//')
        
        log "   Субъект: $subject"
        log "   Издатель: $issuer"
        log "   Действителен до: $not_after"
        
        # Проверка срока действия
        if openssl x509 -in ssl/cert.pem -checkend 86400 >/dev/null 2>&1; then
            log "   ✅ Сертификат действителен"
        else
            warn "   ⚠️  Сертификат истекает в течение 24 часов!"
        fi
    else
        warn "❌ Сертификаты не найдены"
        log "   Запустите: $0 self-signed [domain]"
        log "   Или: $0 letsencrypt domain.com <EMAIL>"
    fi
}

# Показать справку
show_help() {
    echo -e "${BLUE}Tony Clou SSL Certificate Generator${NC}"
    echo ""
    echo "Использование: $0 [КОМАНДА] [ПАРАМЕТРЫ]"
    echo ""
    echo "Команды:"
    echo "  self-signed [domain]           - Создать самоподписанный сертификат (по умолчанию: localhost)"
    echo "  letsencrypt <domain> <email>   - Создать Let's Encrypt сертификат"
    echo "  check                          - Проверить существующие сертификаты"
    echo "  help                           - Показать эту справку"
    echo ""
    echo "Примеры:"
    echo "  $0 self-signed                 # Для localhost"
    echo "  $0 self-signed mydomain.com    # Для конкретного домена"
    echo "  $0 letsencrypt mydomain.com <EMAIL>"
    echo "  $0 check                       # Проверить сертификаты"
    echo ""
}

# Основная логика
main() {
    create_ssl_dir
    
    case "${1:-help}" in
        self-signed)
            generate_self_signed "${2:-localhost}"
            ;;
        letsencrypt)
            generate_letsencrypt "$2" "$3"
            ;;
        check)
            check_certificates
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            error "Неизвестная команда: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Запуск скрипта
main "$@"
