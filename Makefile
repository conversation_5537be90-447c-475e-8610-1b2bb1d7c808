# Makefile для <PERSON> Web Docker

.PHONY: help build start dev stop restart logs clean status health shell test

# Переменные
COMPOSE_FILE = docker-compose.yml
PROJECT_NAME = tony-clou
WEB_PORT = 8080
DEV_PORT = 8081

# Цвета для вывода
GREEN = \033[0;32m
YELLOW = \033[1;33m
RED = \033[0;31m
NC = \033[0m # No Color

help: ## Показать справку
	@echo "$(GREEN)Tony Clou Web Docker Management$(NC)"
	@echo ""
	@echo "Доступные команды:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-12s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "Примеры исп<PERSON>ьзования:"
	@echo "  make build    # Собрать образ"
	@echo "  make start    # Запустить продакшн"
	@echo "  make dev      # Запустить разработку"
	@echo "  make logs     # Посмотреть логи"

build: ## Собрать Docker образ
	@echo "$(GREEN)Сборка Docker образа...$(NC)"
	@mkdir -p logs/nginx
	@docker-compose build --no-cache
	@echo "$(GREEN)✅ Сборка завершена!$(NC)"

start: ## Запустить продакшн версию
	@echo "$(GREEN)Запуск продакшн версии...$(NC)"
	@mkdir -p logs/nginx
	@docker-compose up -d --build
	@echo "$(GREEN)✅ Приложение запущено на http://localhost:$(WEB_PORT)$(NC)"

dev: ## Запустить версию для разработки
	@echo "$(GREEN)Запуск версии для разработки...$(NC)"
	@mkdir -p logs/nginx
	@docker-compose --profile dev up -d tony-clou-dev
	@echo "$(GREEN)✅ Dev версия запущена на http://localhost:$(DEV_PORT)$(NC)"

stop: ## Остановить все контейнеры
	@echo "$(YELLOW)Остановка контейнеров...$(NC)"
	@docker-compose down
	@docker-compose --profile dev down
	@echo "$(GREEN)✅ Контейнеры остановлены!$(NC)"

restart: ## Перезапустить продакшн версию
	@echo "$(YELLOW)Перезапуск продакшн версии...$(NC)"
	@docker-compose restart tony-clou-web
	@echo "$(GREEN)✅ Перезапуск завершен!$(NC)"

logs: ## Показать логи
	@echo "$(GREEN)Показ логов (Ctrl+C для выхода)...$(NC)"
	@docker-compose logs -f

logs-nginx: ## Показать логи Nginx
	@echo "$(GREEN)Логи Nginx:$(NC)"
	@if [ -f logs/nginx/access.log ]; then tail -f logs/nginx/access.log; else echo "$(RED)Логи не найдены$(NC)"; fi

clean: ## Очистить все данные
	@echo "$(RED)⚠️  Это удалит все контейнеры, образы и данные!$(NC)"
	@read -p "Продолжить? (y/N): " confirm && [ "$$confirm" = "y" ] || exit 1
	@echo "$(YELLOW)Очистка данных...$(NC)"
	@docker-compose down -v
	@docker-compose --profile dev down -v
	@docker system prune -a -f
	@echo "$(GREEN)✅ Очистка завершена!$(NC)"

status: ## Показать статус контейнеров
	@echo "$(GREEN)Статус продакшн контейнеров:$(NC)"
	@docker-compose ps
	@echo ""
	@echo "$(GREEN)Статус dev контейнеров:$(NC)"
	@docker-compose --profile dev ps

health: ## Проверить здоровье приложения
	@echo "$(GREEN)Проверка здоровья приложения...$(NC)"
	@if curl -f -s http://localhost:$(WEB_PORT)/health > /dev/null 2>&1; then \
		echo "$(GREEN)✅ Продакшн версия работает (http://localhost:$(WEB_PORT))$(NC)"; \
	else \
		echo "$(RED)❌ Продакшн версия недоступна$(NC)"; \
	fi
	@if curl -f -s http://localhost:$(DEV_PORT) > /dev/null 2>&1; then \
		echo "$(GREEN)✅ Dev версия работает (http://localhost:$(DEV_PORT))$(NC)"; \
	else \
		echo "$(YELLOW)❌ Dev версия недоступна$(NC)"; \
	fi

shell: ## Войти в продакшн контейнер
	@echo "$(GREEN)Вход в продакшн контейнер...$(NC)"
	@docker-compose exec tony-clou-web sh

shell-dev: ## Войти в dev контейнер
	@echo "$(GREEN)Вход в dev контейнер...$(NC)"
	@docker-compose --profile dev exec tony-clou-dev sh

test: ## Запустить тесты Flutter
	@echo "$(GREEN)Запуск тестов Flutter...$(NC)"
	@docker run --rm -v $(PWD):/app -w /app ghcr.io/cirruslabs/flutter:stable flutter test

install: ## Установить зависимости
	@echo "$(GREEN)Установка зависимостей...$(NC)"
	@docker run --rm -v $(PWD):/app -w /app ghcr.io/cirruslabs/flutter:stable flutter pub get

analyze: ## Анализ кода Flutter
	@echo "$(GREEN)Анализ кода Flutter...$(NC)"
	@docker run --rm -v $(PWD):/app -w /app ghcr.io/cirruslabs/flutter:stable flutter analyze

format: ## Форматирование кода Flutter
	@echo "$(GREEN)Форматирование кода Flutter...$(NC)"
	@docker run --rm -v $(PWD):/app -w /app ghcr.io/cirruslabs/flutter:stable dart format .

# Быстрые команды
up: start ## Алиас для start
down: stop ## Алиас для stop
ps: status ## Алиас для status

# Команды для CI/CD
ci-build: ## Сборка для CI/CD
	@docker-compose build

ci-test: ## Тесты для CI/CD
	@docker run --rm -v $(PWD):/app -w /app ghcr.io/cirruslabs/flutter:stable sh -c "flutter pub get && flutter test && flutter analyze"

# Команды для разработки
dev-build: ## Пересобрать dev версию
	@docker-compose --profile dev build tony-clou-dev

dev-logs: ## Логи dev версии
	@docker-compose --profile dev logs -f tony-clou-dev

# Утилиты
open: ## Открыть приложение в браузере
	@echo "$(GREEN)Открытие приложения в браузере...$(NC)"
	@if command -v open > /dev/null; then \
		open http://localhost:$(WEB_PORT); \
	elif command -v xdg-open > /dev/null; then \
		xdg-open http://localhost:$(WEB_PORT); \
	else \
		echo "$(YELLOW)Откройте http://localhost:$(WEB_PORT) в браузере$(NC)"; \
	fi

open-dev: ## Открыть dev версию в браузере
	@echo "$(GREEN)Открытие dev версии в браузере...$(NC)"
	@if command -v open > /dev/null; then \
		open http://localhost:$(DEV_PORT); \
	elif command -v xdg-open > /dev/null; then \
		xdg-open http://localhost:$(DEV_PORT); \
	else \
		echo "$(YELLOW)Откройте http://localhost:$(DEV_PORT) в браузере$(NC)"; \
	fi

# По умолчанию показываем справку
.DEFAULT_GOAL := help
