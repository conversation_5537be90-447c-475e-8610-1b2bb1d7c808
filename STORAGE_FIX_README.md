# Исправление проблемы с flutter_secure_storage в веб-версии

## Проблема

`flutter_secure_storage` не поддерживает веб-платформу, что приводило к ошибкам при попытке запуска приложения в браузере.

## Решение

Создан универсальный сервис `StorageService`, который автоматически выбирает подходящий способ хранения данных в зависимости от платформы:

- **Мобильные платформы (iOS, Android)**: Использует `flutter_secure_storage` для безопасного хранения
- **Веб-платформа**: Использует `shared_preferences` для хранения в localStorage браузера

## Реализация

### 1. Создан новый сервис `lib/shared/services/storage_service.dart`

```dart
class StorageService {
  // Автоматически определяет платформу и использует подходящий способ хранения
  static Future<void> write({required String key, required String? value}) async { ... }
  static Future<String?> read({required String key}) async { ... }
  static Future<void> delete({required String key}) async { ... }
  // ... другие методы
}
```

### 2. Обновлены все файлы, использующие FlutterSecureStorage

Заменены все вызовы `FlutterSecureStorage()` на `StorageService`:

- `lib/shared/data/api.dart`
- `lib/features/splash/presentation/index.dart`
- `lib/features/authorization/presentation/alternative.dart`
- `lib/features/registration/presentation/step3.dart`
- `lib/features/authorization/presentation/verify_code.dart`
- `lib/features/registration/presentation/step2.dart`

### 3. Инициализация в main.dart

Добавлена инициализация сервиса в `main()`:

```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: '.env');
  
  // Initialize storage service
  await StorageService.init();
  
  runApp(const App());
  // ...
}
```

## Преимущества решения

1. **Кроссплатформенность**: Работает на всех поддерживаемых Flutter платформах
2. **Безопасность**: На мобильных платформах данные по-прежнему хранятся в secure storage
3. **Прозрачность**: API остается таким же, как у flutter_secure_storage
4. **Автоматическое определение платформы**: Не требует дополнительных проверок в коде

## Проверка работоспособности

1. **Веб-версия**: `flutter run -d web-server --web-port 8080`
2. **Сборка для веб**: `flutter build web`
3. **Мобильные платформы**: Работают как раньше с secure storage

## Безопасность на веб-платформе

На веб-платформе данные хранятся в localStorage браузера. Хотя это менее безопасно, чем secure storage на мобильных устройствах, это стандартный подход для веб-приложений. Для повышения безопасности можно:

1. Использовать HTTPS
2. Реализовать дополнительное шифрование токенов
3. Использовать короткие сроки жизни токенов
4. Реализовать автоматический logout при закрытии браузера

## Заключение

Проблема с flutter_secure_storage в веб-версии полностью решена. Приложение теперь корректно работает на всех платформах, включая веб-браузеры.
