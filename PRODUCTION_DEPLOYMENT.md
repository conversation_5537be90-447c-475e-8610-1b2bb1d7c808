# Продакшн развертывание Tony Clou Web без портов

## 🎯 Цель
Настроить приложение так, чтобы оно было доступно на стандартных портах (80 для HTTP, 443 для HTTPS) без указания порта в URL.

## 🚀 Быстрый запуск

### Для разработки (с портом)
```bash
make start
# Доступно на: http://localhost:8080/web
```

### Для продакшена (без порта)
```bash
make start-prod
# Доступно на: http://localhost/web
# HTTPS: https://localhost/web
```

## 📁 Файлы конфигурации

### Основные файлы:
- `docker-compose.yml` - Разработка (порт 8080)
- `docker-compose.prod.yml` - Прод<PERSON><PERSON>шн (порты 80/443)
- `Dockerfile` - Разработка
- `Dockerfile.prod` - Продакшн с SSL
- `nginx.conf` - Базовая конфигурация
- `nginx.prod.conf` - Продакшн с HTTPS

## 🔧 Конфигурация портов

### Разработка
```yaml
ports:
  - "8080:80"  # localhost:8080 -> контейнер:80
```

### Продакшн
```yaml
ports:
  - "80:80"    # localhost:80 -> контейнер:80
  - "443:443"  # localhost:443 -> контейнер:443
```

## 🔒 SSL/HTTPS настройка

### Автоматический самоподписанный сертификат
При сборке продакшн образа автоматически создается самоподписанный сертификат для разработки.

### Использование собственных сертификатов
1. Поместите сертификаты в папку `ssl/`:
   ```
   ssl/
   ├── cert.pem    # Сертификат
   └── key.pem     # Приватный ключ
   ```

2. Раскомментируйте SSL конфигурацию в `nginx.prod.conf`:
   ```nginx
   ssl_certificate /etc/nginx/ssl/cert.pem;
   ssl_certificate_key /etc/nginx/ssl/key.pem;
   ```

### Let's Encrypt (рекомендуется для продакшена)
```bash
# Установка certbot
sudo apt install certbot

# Получение сертификата
sudo certbot certonly --standalone -d yourdomain.com

# Копирование сертификатов
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem ssl/cert.pem
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem ssl/key.pem
```

## 🌐 Доменное имя

### Настройка для домена
1. Обновите `docker-compose.prod.yml`:
   ```yaml
   environment:
     - NGINX_HOST=yourdomain.com
   ```

2. Обновите `nginx.prod.conf`:
   ```nginx
   server_name yourdomain.com www.yourdomain.com;
   ```

## 📋 Команды управления

### Makefile команды
```bash
make start          # Разработка (порт 8080)
make start-prod     # Продакшн (порты 80/443)
make stop           # Остановить все
make health         # Проверка разработки
make health-prod    # Проверка продакшена
```

### Скрипт команды
```bash
./docker-scripts.sh start          # Разработка
./docker-scripts.sh start-prod     # Продакшн
./docker-scripts.sh stop           # Остановить все
./docker-scripts.sh health         # Проверка
```

## 🔍 Проверка работоспособности

### HTTP (порт 80)
```bash
curl http://localhost/health
curl http://localhost/web
```

### HTTPS (порт 443)
```bash
curl -k https://localhost/health
curl -k https://localhost/web
```

### Автоматическая проверка
```bash
make health-prod
```

## ⚠️ Важные замечания

### Права доступа
Для использования портов 80 и 443 может потребоваться:
```bash
# Linux/macOS - запуск с sudo
sudo make start-prod

# Или изменение прав Docker
sudo usermod -aG docker $USER
```

### Конфликты портов
Убедитесь, что порты 80 и 443 свободны:
```bash
# Проверка занятых портов
sudo netstat -tulpn | grep :80
sudo netstat -tulpn | grep :443

# Остановка Apache/Nginx если запущены
sudo systemctl stop apache2
sudo systemctl stop nginx
```

## 🚀 Развертывание на сервере

### 1. Подготовка сервера
```bash
# Обновление системы
sudo apt update && sudo apt upgrade -y

# Установка Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Установка Docker Compose
sudo apt install docker-compose-plugin
```

### 2. Развертывание приложения
```bash
# Клонирование репозитория
git clone <your-repo>
cd tony_clou_app

# Запуск продакшн версии
make start-prod
```

### 3. Настройка домена
```bash
# Обновление конфигурации для вашего домена
sed -i 's/yourdomain.com/your-actual-domain.com/g' docker-compose.prod.yml
sed -i 's/yourdomain.com/your-actual-domain.com/g' nginx.prod.conf

# Перезапуск с новой конфигурацией
make stop
make start-prod
```

## 📊 Мониторинг

### Логи
```bash
# Просмотр логов
docker-compose -f docker-compose.prod.yml logs -f

# Логи Nginx
tail -f logs/nginx/access.log
tail -f logs/nginx/error.log
```

### Метрики
```bash
# Статус контейнеров
docker-compose -f docker-compose.prod.yml ps

# Использование ресурсов
docker stats
```

## 🔄 Обновление

```bash
# Остановка
make stop

# Обновление кода
git pull

# Пересборка и запуск
make start-prod
```

Теперь ваше приложение доступно без указания порта в URL! 🎉
