version: '3.8'

services:
  tony-clou-web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: tony-clou-web
    ports:
      - "80:80"
    restart: unless-stopped
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80
    volumes:
      # Монтирование логов для отладки (опционально)
      - ./logs/nginx:/var/log/nginx
    networks:
      - tony-clou-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Опциональный сервис для разработки с hot reload
  # tony-clou-dev:
  #   image: ghcr.io/cirruslabs/flutter:stable
  #   container_name: tony-clou-dev
  #   working_dir: /app
  #   ports:
  #     - "8081:8080"
  #   volumes:
  #     - .:/app
  #     - flutter_pub_cache:/root/.pub-cache
  #   command: >
  #     sh -c "flutter config --enable-web &&
  #            flutter pub get &&
  #            flutter run -d web-server --web-hostname 0.0.0.0 --web-port 8080"
  #   networks:
  #     - tony-clou-network
  #   profiles:
  #     - dev
  #   environment:
  #     - FLUTTER_WEB=true

networks:
  tony-clou-network:
    driver: bridge

volumes:
  flutter_pub_cache:
    driver: local
