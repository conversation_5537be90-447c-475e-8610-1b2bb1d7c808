# Multi-stage build для Flutter Web приложения (Production)
FROM ghcr.io/cirruslabs/flutter:stable AS build

# Установка рабочей директории
WORKDIR /app

# Копирование файлов конфигурации
COPY pubspec.yaml pubspec.lock ./

# Установка зависимостей
RUN flutter pub get

# Копирование исходного кода
COPY . .

# Включение веб-поддержки Flutter
RUN flutter config --enable-web

# Сборка веб-приложения для продакшена с базовым путем /web/
RUN flutter build web --release --base-href /web/

# Production stage с nginx
FROM nginx:alpine

# Установка дополнительных пакетов для SSL
RUN apk add --no-cache openssl curl

# Копирование собранного приложения
COPY --from=build /app/build/web /usr/share/nginx/html

# Копирование продакшн конфигурации nginx
COPY nginx.prod.conf /etc/nginx/nginx.conf

# Создание директории для SSL сертификатов
RUN mkdir -p /etc/nginx/ssl

# Создание директории для логов
RUN mkdir -p /var/log/nginx

# Создание самоподписанного сертификата (для разработки)
RUN openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/nginx/ssl/key.pem \
    -out /etc/nginx/ssl/cert.pem \
    -subj "/C=RU/ST=Moscow/L=Moscow/O=TonyClou/CN=localhost"

# Открытие портов
EXPOSE 80 443

# Запуск nginx
CMD ["nginx", "-g", "daemon off;"]
