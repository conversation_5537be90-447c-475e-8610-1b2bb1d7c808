# 🚀 Развертывание <PERSON> на prolesfero.space

## 📋 Обзор

Это руководство по развертыванию Flutter веб-приложения Tony <PERSON>lou на домене `prolesfero.space` с полной поддержкой SSL/HTTPS.

## 🎯 Что будет развернуто

- **Домен**: prolesfero.space
- **Поддомены**: www.prolesfero.space (автоматический редирект)
- **Протоколы**: HTTP (редирект на HTTPS) + HTTPS
- **Путь приложения**: `/web`
- **Полные URL**:
  - https://prolesfero.space/web
  - https://www.prolesfero.space/web

## 🔧 Предварительные требования

### На сервере должно быть установлено:
- Docker (версия 20.10+)
- Docker Compose (версия 2.0+)
- Git
- Доступ к портам 80 и 443

### DNS настройки:
```
A    prolesfero.space     -> IP_ВАШЕГО_СЕРВЕРА
A    www.prolesfero.space -> IP_ВАШЕГО_СЕРВЕРА
```

## 🚀 Быстрое развертывание

### 1. Клонирование репозитория
```bash
git clone <your-repo-url> tony_clou_app
cd tony_clou_app
```

### 2. Автоматическое развертывание
```bash
# Полное развертывание с интерактивной настройкой SSL
./deploy-prolesfero.sh

# Или используя Make
make deploy-prolesfero
```

### 3. Ручное развертывание

#### Шаг 1: Создание SSL сертификата

**Вариант A: Let's Encrypt (рекомендуется для продакшена)**
```bash
# Интерактивная настройка
make ssl-prolesfero

# Или с указанием email
make ssl-letsencrypt EMAIL=<EMAIL>
```

**Вариант B: Самоподписанный (для тестирования)**
```bash
make ssl-generate
```

#### Шаг 2: Запуск приложения
```bash
make start-prod
```

## 📁 Структура конфигурации

### Основные файлы:
- `nginx.prod.conf` - Nginx конфигурация с SSL
- `docker-compose.prod.yml` - Docker Compose для продакшена
- `Dockerfile.prod` - Dockerfile с SSL поддержкой
- `generate-ssl.sh` - Скрипт генерации SSL сертификатов
- `deploy-prolesfero.sh` - Скрипт автоматического развертывания

### SSL сертификаты:
```
ssl/
├── cert.pem    # SSL сертификат
├── key.pem     # Приватный ключ
└── openssl.conf # Конфигурация OpenSSL (временный)
```

## 🔒 SSL Конфигурация

### Поддерживаемые типы сертификатов:

1. **Let's Encrypt** (бесплатный, автоматическое обновление)
   - Валидный для всех браузеров
   - Автоматическое обновление каждые 90 дней
   - Требует домен, указывающий на сервер

2. **Самоподписанный** (для разработки/тестирования)
   - Предупреждение в браузере
   - Подходит для внутреннего использования

### Команды управления SSL:
```bash
make ssl-check          # Проверить текущие сертификаты
make ssl-generate       # Создать самоподписанный
make ssl-prolesfero     # Создать Let's Encrypt для prolesfero.space
```

## 🌐 Nginx конфигурация

### Основные особенности:
- **HTTP → HTTPS редирект**: Все HTTP запросы автоматически перенаправляются на HTTPS
- **HTTP/2 поддержка**: Включен для лучшей производительности
- **Безопасность**: Настроены заголовки безопасности и CSP
- **Кэширование**: Статические файлы кэшируются на 1 год
- **SPA роутинг**: Корректная обработка Flutter роутинга

### Поддерживаемые домены:
- prolesfero.space
- www.prolesfero.space

## 📊 Мониторинг и управление

### Проверка статуса:
```bash
make health-prod        # Проверка здоровья приложения
make check-prolesfero   # Проверка конкретно prolesfero.space
docker-compose -f docker-compose.prod.yml ps  # Статус контейнеров
```

### Просмотр логов:
```bash
make logs-prod          # Логи приложения
make logs-prod-follow   # Логи в реальном времени
```

### Управление:
```bash
make start-prod         # Запуск
make stop-prod          # Остановка
make restart-prod       # Перезапуск
make clean              # Полная очистка
```

## 🔧 Обновление приложения

### Обновление кода:
```bash
git pull origin main
make restart-prod
```

### Обновление SSL сертификата:
```bash
# Let's Encrypt обновляется автоматически
# Для ручного обновления:
make ssl-prolesfero
make restart-prod
```

## 🐛 Устранение неполадок

### Проблема: Сайт недоступен
```bash
# Проверить статус контейнеров
docker-compose -f docker-compose.prod.yml ps

# Проверить логи
docker-compose -f docker-compose.prod.yml logs

# Проверить порты
netstat -tlnp | grep -E ':(80|443)'
```

### Проблема: SSL ошибки
```bash
# Проверить сертификаты
make ssl-check

# Пересоздать сертификаты
make ssl-prolesfero
make restart-prod
```

### Проблема: 404 ошибки
```bash
# Проверить nginx конфигурацию
docker-compose -f docker-compose.prod.yml exec tony-clou-web nginx -t

# Перезапустить nginx
docker-compose -f docker-compose.prod.yml restart
```

## 📈 Производительность

### Оптимизации:
- ✅ Gzip сжатие включено
- ✅ HTTP/2 поддержка
- ✅ Кэширование статических файлов
- ✅ Минификация Flutter кода
- ✅ CSP заголовки для безопасности

### Мониторинг:
```bash
# Проверка производительности
curl -w "@curl-format.txt" -o /dev/null -s https://prolesfero.space/web

# Проверка размера ответа
curl -H "Accept-Encoding: gzip" -s https://prolesfero.space/web | wc -c
```

## 🎉 Готово!

После успешного развертывания ваше приложение будет доступно по адресам:

- **Основной**: https://prolesfero.space/web
- **С www**: https://www.prolesfero.space/web
- **HTTP редирект**: http://prolesfero.space → https://prolesfero.space/web

### Полезные ссылки:
- **Приложение**: https://prolesfero.space/web
- **Проверка здоровья**: https://prolesfero.space/health
- **SSL тест**: https://www.ssllabs.com/ssltest/analyze.html?d=prolesfero.space

---

**Поздравляем! 🎊 Tony Clou успешно развернут на prolesfero.space с полной поддержкой SSL!**
