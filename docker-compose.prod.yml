services:
  tony-clou-web:
    build:
      context: .
      dockerfile: Dockerfile.prod
    container_name: tony-clou-web-prod
    ports:
      - "80:80"
      - "443:443"
    restart: unless-stopped
    environment:
      - NGINX_HOST=tonyclou.prolesfero.space/
      - NGINX_PORT=80
    volumes:
      # Монтирование SSL сертификатов (если есть)
      - ./ssl:/etc/nginx/ssl:ro
      # Монтирование логов
      - ./logs/nginx:/var/log/nginx
    networks:
      - tony-clou-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  tony-clou-network:
    driver: bridge

