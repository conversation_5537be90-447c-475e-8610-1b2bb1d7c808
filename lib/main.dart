import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:tony_clou_app/app.dart';
import 'package:tony_clou_app/shared/services/storage_service.dart';

void main() async {
  // load .env file
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: '.env');

  // Initialize storage service
  await StorageService.init();

  runApp(const App());

  // Enable verbose logging for debugging (remove in production)
  // OneSignal.Debug.setLogLevel(OSLogLevel.verbose);
  // Initialize with your OneSignal App ID
  if (!kIsWeb) {
    OneSignal.initialize(dotenv.get('NOTIFICATIONS_KEY'));
    OneSignal.Notifications.requestPermission(true);
  }
}

/// ✅ Обнуление первого января Coins
/// 
/// ✅ Сканирование QR через галерею
/// 
/// ✅ Поиск людей и чеков (Имя / Фамилия)
/// 
/// ✅ Главный экран:
/// ✅ 1. Где нас найти (Адреса)
/// ✅ 2. Баланс
/// ✅ 3. О нас
/// ✅ 4. Контакты (Ссылка на сайт, Номер телефона (Антона и Ильи и По сотрудничеству (Елизавета)), Почта)
/// ✅ 5. Админка (если админ)
/// 
/// ✅ До 22-го
/// 
/// ✅ - Push уведомления
/// ✅ Каждый месяц первого числа рссылка про баланс пользователя
/// ✅ И 1 декабря – "успевайте воспользоваться"
/// ✅ На день рождения 2000 монет
/// 
/// ✅ Картинки на фоны
/// 
/// ✅ – Rustore публикация
/// ✅ Альтернативный вход
/// 
/// ✅ Посмотреть TestFlight
/// ✅ Ответ: Лучше после Rustore покупать apple developer access
/// 
/// ✅ 1000 монет при регистрации
/// 
/// ✅ – Контакты
/// ✅ Генеральный директор
/// ✅ Работа с резидентами
/// ✅ По сотруднечеству
/// 
/// ✅ VK ссылка
/// 
/// ✅ – За что начисляются баллы
/// ✅ До 50 % на одежду 
/// ✅ До 25% на аксессуары и новую коллекцию 
/// ✅ До 10 процесса на иные продукты продакшена 
/// 
/// ✅ Геймс До 100% оплаты на одежду.
/// 
/// ---
/// 
/// QR профиля
/// 
/// Админ может смотреть историю 
/// 
/// Геймификация (Игра на получение Coins / Gems)
/// 
/// История уведомлений
/// 
/// Ручные уведомления
/// 
/// 
/// 
/// 
// -----BEGIN RSA PRIVATE KEY-----
// MIIJKgIBAAKCAgEAzc9gLB4OdEkwLDpSVHIdBBIrVvLtte3ZeVCpmdnSGZOelD2h
// Lk8fbLlptL0P0Cs0b4mhYrQw7JChXqcODDTBC5rkTolaLEqupkAPieOhWxbvcoBW
// JQCNEsvXogzfLKKmPWfypgfLdectEBb4eynxRT7NB02dSchIdv0bT9BK9debQE87
// 9KY8cDgRlV+oEytuL7ORqw7ts+T4urCRvlOVg/CakPBuwJTEgZ7TB79wWjq6QEdu
// TEBf6hwE95r/RG0QrLK4mGZRLc51/9GNaLHzFfHiKkOYfiuDLoIZpyTDMVJqMmKG
// S+/LeAecPT1IEdK9vpTwwRXh8Ky11PR8xSEpeSSxF6s2ZnqMHEd/MAfYS9fiWNKk
// JcAHevdZw0zwn//WnjPO412dUo8wv2nzGd4HLGGVLEPBMMM+lKTkmqHh5kmMco3v
// SLQq0fkAWP6uBw16TYYzpAlm8hFAx4fu3GwK1FtGZbkT69tNG0ExH8gqsq/1mFqX
// XUY7mBF/pXhz1mr92GB9AmNl+zHn1FX5x8ibDH/QSwxOhm8UQr/406PWXyoBDfVV
// AYWokcbe2N5Ap0AdwV39mVICA53fVr011R/Q+g1vsVvGjtym8Y0ydjg2YTaAKyPb
// baWEp2q2Xyjz7v0o9z/1aFpPhk18aHTcYHN25S4QvBqJ++AlsjaXioZLgqMCAwEA
// AQKCAgA+hYH8xQC1GMq2lN0P5D6mTvK1jkTiYKgRL4zmPQKTXIHvHK3LSKQOPmE7
// U7frEET0oUXSm1TFeMZc37jnwlrmsJ4ssZMgI2vc+1b9F5ab+mKxUQVaZkl07VHL
// W8esYpjHzL0s7O9FGP5ahe/kUoCF/acBUfW3RU2mQAu8+lTbTC1QowFet+ViXdYR
// n6aKdEYb3Zwh7/4MCUH6TMtpmzCvjTcXUsMCY+8yRVJsZx61emO8RTbfmSrKDsr/
// gSmM2ukW3ZAwU9zTYoX8OeeQZ4amIv4GwtQfeA544XLzkLpvUE8MkVuk0+rmwsEW
// 5XfwWinkLJDax1AE8iKm18LTi9hx9lnvWm8vjbc/doOIoKakj1W3RQ2QcxSSAaSk
// HOgiZacBhf1nWaubsoF/UhUpvrZmpSJCQDa5tKFEy5om14oVYqHBajHCSi7XNVCa
// 1zOu0fV2nvUzWEQF9AbGM9sS/Es/2Vxx64NzssdFehf7rQuOs6crt2uLioKLBOrj
// McJI9SpBt8yl5snNizzR1SXWF7X4W8gttHqRU9g86ChCmryJwVnZdR8Bp/GatBd5
// 02Tw7eHzu4kDLFLN7jdbmQ+ql+02FCbx4n7d5iR6IxYp50Q9+5FMqvKew662G6rS
// +PuZO+kyEx0bKGosz1+6dHO5WuIen8D/p6lHdoX3D7bLFQhHeQKCAQEA5ttds+mo
// sb695RWJuJ5mSoEia0yoiXnhJesY9NcTZ/26Ainmg7ro3Wc8sV0PfhusEUQyieg4
// BAZtjeJjzXHNAzWQ3I73je5fZISvbDRd+8evBzshxjUi8HagjDJw4nHQlnzdptQG
// +hX8QS8ZpQcaNRW3b4ifDVA7GXz87pfp1zL6XN2KjTby7w/TuHBouBkVe+5/XXcs
// mTDcD8SQZeC73Bk78KmpEAeCGBcVzDqtx1Z0XNth8fXLlIObeOm9dN3mJ7PyYUbI
// uNWgy1SXMAjH91ttFLeaZI3YmqcEy0Vl8VnUYKYXOxYWy2IEdinc4Nx9sOnyxFLv
// xF/oLHL4U8YZhQKCAQEA5DmqokkGgCDlLubMtyhx6n9bocx03BFL6yQW1lDS8d1M
// se9K6gW6/1JJ7i/eejNqDH0qTKCw5uNfABUmKATl+RVrGXn58Zd7E9YK9wjt+4Ne
// mL3Rl+1pcT+zqQA2T7fzUJgPrXkCQis5Q+hjF05kWVDCN6O1l/SdToyg8ZUdsOa+
// +RZMI0mA8AsMvco9Sn+s2bmUsQlY54Xb0v9DUbihfiFJmkw76gv1ZBiG/0QJ0TU+
// +DNFO3p6MXxrk8WzJSwRCcM6q/MfYLk6VnFfC7MRE9hmADu1CEpgw8ftuhapJbNc
// rh+sMSupRkCveNaFL14xt1qdJX64EV1VAXCEG06QBwKCAQEAmOKqOZ4CtEQH6pDK
// 9lMglzLSIpFQBEuNCdmaW3ucRIBKEWjCWncMBKR8E1H4EqIs3/p0JdR+VFwVbaOC
// SOqWxQtSdl7QH1TAJAejTVuY6keh6H4Grgc+rlVBmddF3xFU/HSRLgy42iJ5Pon8
// x8feWU2af+jQvLZnjUezEifVG0mNeLbXLRI47I1kv37iOxmRmtOl0bY8+O7criyV
// HIMqpmanxYf2iMEQLPgM3YX21E6zHVPEl8MaIa7O5dKWP7E83QpU+IsyHwaLSF0r
// Cwqls3D+2nuFahZuXm1BxYSeMKo29hLIy76jNeoh3kd3GguryeAFgHMUis6hKd6y
// CnjlKQKCAQEAgNBDjQFml975PDWsihxb350unO4Hb0ab8QYXlbNcvILJjQyg3eGK
// IB3T+IowKgzm6FGlFEcGUes3bZaCSMgq/SGnp2N/PWrgXtvKqI/zsqf9Nny81eF8
// SB1KTDMAdJJA1NZe0uyf57T5x2hctYTbgsXgOQM/tZqtRu580J33eidi1rhNFQWh
// rtqtwFlrqqbRaroiQKIj+dlFr5u/5VouXEBxiJNKpdlGEc7+Mwm1+Mzy06gshVul
// qFSva2KEsJBktXvpm6/HoBIr6SpoGPRiyS60s6D/lsRURWyMsyGjKtJHl0ftKnuY
// OTy9TZjW51tk+DY+yDDzt2lUV1GoXrlC9QKCAQEA2JED5G7Pvwz8002T0xa5E5Np
// DBzXr0bkggLMKq1O0r7TEQitZKQiTdADksP2kP1YHNOEbqygBjaaA2a0tLhcXsvD
// nHTpROOCgmZd1HKs89kHt5E4cfxx6ey1Wavzbkz4W8IDSkJ+9D9OYNllKERhe8zD
// 1IjZrzDd/QdcjCsMdDiTEhRN/kCPKcOk9T28wFntqDat4yVqRyfw9LgQE95Fo0JG
// 60bz7rVQHnkKzTKOz1bmy8d9pzb04SlhLA6ShihjuPEzF8M6Hog2KtAjKBdcp+8E
// JPLrKaaukaiCRiIQPkql5SHzzb8r90aAOaF6wuJqYxMaC7gf4b6vyuqGxpPcag==
// -----END RSA PRIVATE KEY-----

// -----BEGIN CERTIFICATE REQUEST-----
// MIIEcTCCAlkCAQAwLDELMAkGA1UEBhMCUlUxHTAbBgNVBAMMFHd3dy5wcm9sZXNm
// ZXJvLnNwYWNlMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAzc9gLB4O
// dEkwLDpSVHIdBBIrVvLtte3ZeVCpmdnSGZOelD2hLk8fbLlptL0P0Cs0b4mhYrQw
// 7JChXqcODDTBC5rkTolaLEqupkAPieOhWxbvcoBWJQCNEsvXogzfLKKmPWfypgfL
// dectEBb4eynxRT7NB02dSchIdv0bT9BK9debQE879KY8cDgRlV+oEytuL7ORqw7t
// s+T4urCRvlOVg/CakPBuwJTEgZ7TB79wWjq6QEduTEBf6hwE95r/RG0QrLK4mGZR
// Lc51/9GNaLHzFfHiKkOYfiuDLoIZpyTDMVJqMmKGS+/LeAecPT1IEdK9vpTwwRXh
// 8Ky11PR8xSEpeSSxF6s2ZnqMHEd/MAfYS9fiWNKkJcAHevdZw0zwn//WnjPO412d
// Uo8wv2nzGd4HLGGVLEPBMMM+lKTkmqHh5kmMco3vSLQq0fkAWP6uBw16TYYzpAlm
// 8hFAx4fu3GwK1FtGZbkT69tNG0ExH8gqsq/1mFqXXUY7mBF/pXhz1mr92GB9AmNl
// +zHn1FX5x8ibDH/QSwxOhm8UQr/406PWXyoBDfVVAYWokcbe2N5Ap0AdwV39mVIC
// A53fVr011R/Q+g1vsVvGjtym8Y0ydjg2YTaAKyPbbaWEp2q2Xyjz7v0o9z/1aFpP
// hk18aHTcYHN25S4QvBqJ++AlsjaXioZLgqMCAwEAAaAAMA0GCSqGSIb3DQEBCwUA
// A4ICAQByVCrtzGZcKXTXJ1SGCnuy/4JMqV2hvhrmu3LY+QE1EHoQzjp5s2gX3Tg0
// MT0Iw11ZFbVUoMjtF4yv/PjWiS2FPMALeuXApmi9oGcb9mlVvlDZASDfMAgeLAiE
// DTS0qlkBxZkQ0MhDacaE7LqSTe9IS74nNjKGJW2ged2OFhVhdiCt3mZRCqosSwYt
// MAZuOaHJNYoA9hHb/ZveyoQ9RS+vjJW7GX4ugNnEqcLDcq0XRGvh8OnG+JJZpotJ
// CIeUU7RXK5YR4Npfq5UcpypwhVCDZvgqFYB19Cdj+h6Dy71PkG+HCJrEgXSHu9T8
// qfw/DfKBKBBGLHKo4PQ95VkKQJhupEQOniHHIfmSenOINYD/mAii8mDkf2Iqsg6Y
// +kn7W8ZFGFamDdilCdbe6OUZSNNSFdSKVruRZofFvYpha2ecXlSzGiN0G6PxIMwc
// UUEqAWWpYXOIDq3+j341dZKYXKscBi0J8gWDPZb1fBf5jtz7miYTTXyk66PuUXe+
// RK3Fg4J0eHMB3NbJHlLxph/bF8SYCpBDn90OhTOTcdSuS/Wo6nlLnJ/wfvk0sQ8H
// 5T4efY4iAmHZwNMpJHR9GXxcnrhu4Su7AynRoE0HMhK+8eLIwsM1XGI+TBCtQnN8
// zhEYe2Qf8+e67aZ6jtwGrh1I+6zPXELif5bQXY9/8LpSTy8j8g==
// -----END CERTIFICATE REQUEST-----

