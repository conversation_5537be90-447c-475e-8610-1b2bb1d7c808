import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:tony_clou_app/app.dart';
import 'package:tony_clou_app/shared/services/storage_service.dart';

void main() async {
  // load .env file
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: '.env');

  // Initialize storage service
  await StorageService.init();

  runApp(const App());

  // Enable verbose logging for debugging (remove in production)
  // OneSignal.Debug.setLogLevel(OSLogLevel.verbose);
  // Initialize with your OneSignal App ID
  if (!kIsWeb) {
    OneSignal.initialize(dotenv.get('NOTIFICATIONS_KEY'));
    OneSignal.Notifications.requestPermission(true);
  }
}

/// ✅ Обнуление первого января Coins
/// 
/// ✅ Сканирование QR через галерею
/// 
/// ✅ Поиск людей и чеков (Имя / Фамилия)
/// 
/// ✅ Главный экран:
/// ✅ 1. Где нас найти (Адреса)
/// ✅ 2. Баланс
/// ✅ 3. О нас
/// ✅ 4. Контакты (Ссылка на сайт, Номер телефона (Антона и Ильи и По сотрудничеству (Елизавета)), Почта)
/// ✅ 5. Админка (если админ)
/// 
/// ✅ До 22-го
/// 
/// ✅ - Push уведомления
/// ✅ Каждый месяц первого числа рссылка про баланс пользователя
/// ✅ И 1 декабря – "успевайте воспользоваться"
/// ✅ На день рождения 2000 монет
/// 
/// ✅ Картинки на фоны
/// 
/// ✅ – Rustore публикация
/// ✅ Альтернативный вход
/// 
/// ✅ Посмотреть TestFlight
/// ✅ Ответ: Лучше после Rustore покупать apple developer access
/// 
/// ✅ 1000 монет при регистрации
/// 
/// ✅ – Контакты
/// ✅ Генеральный директор
/// ✅ Работа с резидентами
/// ✅ По сотруднечеству
/// 
/// ✅ VK ссылка
/// 
/// ✅ – За что начисляются баллы
/// ✅ До 50 % на одежду 
/// ✅ До 25% на аксессуары и новую коллекцию 
/// ✅ До 10 процесса на иные продукты продакшена 
/// 
/// ✅ Геймс До 100% оплаты на одежду.
/// 
/// ---
/// 
/// QR профиля
/// 
/// Админ может смотреть историю 
/// 
/// Геймификация (Игра на получение Coins / Gems)
/// 
/// История уведомлений
/// 
/// Ручные уведомления
/// 