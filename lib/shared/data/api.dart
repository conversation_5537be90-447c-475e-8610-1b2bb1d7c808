import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:logger/logger.dart';
import 'package:tony_clou_app/shared/helpers/context_service.dart';
import 'package:tony_clou_app/shared/services/storage_service.dart';
import 'package:tony_clou_app/shared/widgets/utility/snackbar.dart';

class API {
  static final dio =
      Dio()
        ..options.validateStatus = (status) {
          // Позволяем Dio не выбрасывать исключение для всех статусов от 200 до 700
          return status != null && status >= 200 && status < 700;
        };

  // local / external
  static final mode = dotenv.maybeGet('MODE') ?? 'local';

  static final localURL = dotenv.get('API_URL_LOCAL');
  static final externalURL = dotenv.get('API_URL_EXTERNAL');

  static final postfix = dotenv.get('API_POSTFIX');

  static final secretAPI = dotenv.maybeGet('SECRET_API_KEY') ?? '';

  // получение базового url
  static String getBaseURL() {
    if (mode == 'local') {
      return '$localURL$postfix';
    } else {
      return '$externalURL$postfix';
    }
  }

  // обработка запросов
  static Future<Response<T>> request<T>({
    String url = '/',
    Object body = const {},
    String method = 'GET',
    String authorization = '',
    T Function(Map<String, dynamic>)? fromJson,
    Options? options,
    BuildContext? context,
  }) async {
    final logger = Logger(printer: PrettyPrinter());
    try {
      final accessToken =
          await StorageService.read(key: 'accessToken') ?? authorization;

      // try request
      logger.i('--- INPUT ---');
      logger.d(
        'URL: ${getBaseURL() + url} \nSECRET_API: $secretAPI\nBODY: ${body.toString()}\nACCESS_TOKEN: $accessToken',
      );
      final result = await dio.request(
        '${getBaseURL()}$url',
        data: body,
        options: Options(
          method: options?.method ?? method,
          contentType: options?.contentType ?? 'application/json',
          headers: options?.headers ?? {'Authorization': 'Bearer $accessToken'},
        ),
      );

      // cast and output response
      logger.i('--- OUTPUT ---');
      logger.d(result.data);

      final globalContext = NavigationService.navigatorKey.currentContext;

      if (context != null || globalContext != null) {
        if (result.data['message'] != null) {
          ScaffoldMessenger.of(context ?? globalContext!).showSnackBar(
            CustomSnackbar(
              text: '${result.statusCode}: ${result.data['message']}',
              type:
                  (result.statusCode ?? 0) >= 400
                      ? SnackBarType.error
                      : SnackBarType.common,
            ).toSnackBar(context ?? globalContext!),
          );
        }
      }

      return Response(
        data: fromJson?.call(result.data) ?? result.data,
        requestOptions: result.requestOptions,
        statusCode: result.statusCode,
        statusMessage: result.statusMessage,
      );
    } catch (e) {
      // catch if error
      logger.e(e);
      return Response(
        requestOptions: RequestOptions(),
        statusCode: 500,
        statusMessage: e.toString(),
      );
    }
  }

  // New function to fetch image with JWT token
  static Future<Response<Uint8List>> fetchImage({
    required String imageUrl,
    String authorization = '',
    BuildContext? context,
  }) async {
    final logger = Logger(printer: PrettyPrinter());
    try {
      final accessToken =
          await StorageService.read(key: 'accessToken') ?? authorization;

      // Log request
      logger.i('Fetch Image FETCH IMAGE:');
      logger.d(
        'URL: ${getBaseURL() + imageUrl} \nSECRET_API: $secretAPI\nACCESS_TOKEN: $accessToken',
      );
      final result = await dio.get(
        '${getBaseURL()}$imageUrl',
        options: Options(
          method: 'GET',
          responseType: ResponseType.bytes,
          headers: {'Authorization': 'Bearer $accessToken'},
        ),
      );

      // Handle response
      // final globalContext = NavigationService.navigatorKey.currentContext;
      // if (context != null || globalContext != null) {
      //   if (result.statusCode != null && result.statusCode! >= 400) {
      //     ScaffoldMessenger.of(context ?? globalContext!).showSnackBar(
      //       CustomSnackbar(
      //         text: '${result.statusCode}: Error fetching image',
      //         type: SnackBarType.error,
      //       ).toSnackBar(context ?? globalContext!),
      //     );
      //   }
      // }

      if (result.statusCode != 200) {
        throw Exception(
          'Failed to fetch image: ${result.statusCode} ${result.statusMessage}',
        );
      }

      // Convert response to Uint8List
      final imageBytes = Uint8List.fromList(result.data as List<int>);
      return Response(
        data: imageBytes,
        requestOptions: result.requestOptions,
        statusCode: result.statusCode,
        statusMessage: result.statusMessage,
      );
    } catch (e) {
      // Log error
      logger.e('Fetch error: $e');
      return Response(
        requestOptions: RequestOptions(),
        statusCode: 500,
        statusMessage: e.toString(),
      );
    }
  }
}
