import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Универсальный сервис для хранения данных
/// Использует flutter_secure_storage на мобильных платформах
/// и shared_preferences на веб-платформе
class StorageService {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();
  static SharedPreferences? _prefs;

  /// Инициализация сервиса (необходимо вызвать перед использованием на веб)
  static Future<void> init() async {
    if (kIsWeb) {
      _prefs = await SharedPreferences.getInstance();
    }
  }

  /// Записать значение по ключу
  static Future<void> write({
    required String key,
    required String? value,
  }) async {
    if (value == null) {
      await delete(key: key);
      return;
    }

    if (kIsWeb) {
      await _ensurePrefsInitialized();
      await _prefs!.setString(key, value);
    } else {
      await _secureStorage.write(key: key, value: value);
    }
  }

  /// Прочитать значение по ключу
  static Future<String?> read({required String key}) async {
    if (kIsWeb) {
      await _ensurePrefsInitialized();
      return _prefs!.getString(key);
    } else {
      return await _secureStorage.read(key: key);
    }
  }

  /// Удалить значение по ключу
  static Future<void> delete({required String key}) async {
    if (kIsWeb) {
      await _ensurePrefsInitialized();
      await _prefs!.remove(key);
    } else {
      await _secureStorage.delete(key: key);
    }
  }

  /// Удалить все значения
  static Future<void> deleteAll() async {
    if (kIsWeb) {
      await _ensurePrefsInitialized();
      await _prefs!.clear();
    } else {
      await _secureStorage.deleteAll();
    }
  }

  /// Проверить, содержит ли хранилище ключ
  static Future<bool> containsKey({required String key}) async {
    if (kIsWeb) {
      await _ensurePrefsInitialized();
      return _prefs!.containsKey(key);
    } else {
      return await _secureStorage.containsKey(key: key);
    }
  }

  /// Получить все ключи
  static Future<Set<String>> getAllKeys() async {
    if (kIsWeb) {
      await _ensurePrefsInitialized();
      return _prefs!.getKeys();
    } else {
      final Map<String, String> allValues = await _secureStorage.readAll();
      return allValues.keys.toSet();
    }
  }

  /// Убедиться, что SharedPreferences инициализированы
  static Future<void> _ensurePrefsInitialized() async {
    _prefs ??= await SharedPreferences.getInstance();
  }
}
