import 'package:flutter/material.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';

class CustomTextFieldTheme {
  static final lightTheme = InputDecorationTheme(
    hoverColor: AppColors.lightHover,
    constraints: const BoxConstraints(minHeight: 46.0),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(12.0),
      borderSide: const BorderSide(width: 1.0, color: AppColors.lightStroke),
    ),
    disabledBorder: OutlineInputBorder(
      borderSide: const BorderSide(
        width: 1.0,
        color: AppColors.lightStroke,
        style: BorderStyle.solid,
      ),
      borderRadius: BorderRadius.circular(12.0),
    ),
    enabledBorder: OutlineInputBorder(
      borderSide: const BorderSide(
        width: 1.0,
        color: AppColors.lightStroke,
        style: BorderStyle.solid,
      ),
      borderRadius: BorderRadius.circular(12.0),
    ),
    focusedBorder: OutlineInputBorder(
      borderSide: BorderSide(
        width: 1.0,
        color: AppColors.lightSecondary.withOpacity(0.5),
        style: BorderStyle.solid,
      ),
      borderRadius: BorderRadius.circular(12.0),
    ),
    errorBorder: OutlineInputBorder(
      borderSide: const BorderSide(
        width: 1.0,
        color: AppColors.lightError,
        style: BorderStyle.solid,
      ),
      borderRadius: BorderRadius.circular(12.0),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderSide: const BorderSide(
        width: 1.0,
        color: AppColors.lightError,
        style: BorderStyle.solid,
      ),
      borderRadius: BorderRadius.circular(12.0),
    ),
    filled: true,
    fillColor: AppColors.lightSurface,
    labelStyle: Fonts.labelSmall,
    counterStyle: Fonts.bodySmall,
    hintStyle: Fonts.bodyMedium.merge(
      const TextStyle(color: AppColors.lightDescription),
    ),
    errorStyle: Fonts.bodyMedium.merge(
      const TextStyle(color: AppColors.lightError),
    ),
    contentPadding: const EdgeInsets.symmetric(
      horizontal: 16.0,
      vertical: 20.0,
    ),
  );
}
