import 'package:flutter/material.dart';
import 'package:tony_clou_app/shared/helpers/context_service.dart';

class Wrapper extends StatefulWidget {
  const Wrapper({
    super.key,
    required this.body,
    this.appBar,
    this.bottomNavigationBar,
    this.backgroundColor,
    this.withSafeArea = true,
    this.extendBody = false,
    this.drawer,
    this.floatingActionButton,
  });

  final Widget body;
  final PreferredSizeWidget? appBar;
  final Widget? bottomNavigationBar;
  final Color? backgroundColor;
  final bool withSafeArea;
  final bool extendBody;
  final Widget? drawer;
  final Widget? floatingActionButton;

  @override
  State<Wrapper> createState() => _WrapperState();
}

class _WrapperState extends State<Wrapper> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    NavigationService.navigatorKey = _scaffoldKey;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey, // Используем локальный ключ
      appBar: widget.appBar,
      bottomNavigationBar: widget.bottomNavigationBar,
      backgroundColor: widget.backgroundColor,
      extendBody: widget.extendBody,
      endDrawer: widget.drawer,
      floatingActionButton: widget.floatingActionButton,
      body: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: widget.withSafeArea ? SafeArea(child: widget.body) : widget.body,
      ),
    );
  }
}
