import 'dart:typed_data';

import 'package:auto_route/auto_route.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tony_clou_app/shared/widgets/containers/card/index.dart';

class ImageFromRequest extends StatelessWidget {
  const ImageFromRequest({
    super.key,
    required this.request,
    this.width,
    this.height,
    this.borderRadius = 4.0,
    this.fit = BoxFit.cover,
    this.onTap,
    this.border,
  });

  final double? width;
  final double? height;
  final double borderRadius;
  final Border? border;
  final Future<Response<Uint8List>> Function() request;
  final void Function()? onTap;
  final BoxFit fit;

  @override
  Widget build(BuildContext context) {
    // final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return CustomCard(
      onTap: onTap,
      padding: EdgeInsets.zero,
      borderRadius: borderRadius,
      border: border ?? Border.all(color: Colors.transparent, width: 0),
      color: Colors.transparent,
      clipBehavior: Clip.hardEdge,
      child: FutureBuilder<Response<Uint8List>>(
        future: request(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(
              child: SizedBox(
                height: height,
                width: width,
                child: CupertinoActivityIndicator(),
              ),
            );
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          } else if (snapshot.hasData && snapshot.data!.data != null) {
            return Center(
              child: Image.memory(
                width: width,
                height: height,
                snapshot.data!.data!,
                fit: fit,
                errorBuilder: (context, error, stackTrace) {
                  return const Text('Failed to load image');
                },
              ),
            );
          } else {
            return const Center(child: Text('No image found'));
          }
        },
      ),
    );
  }
}

class ImagePreviews {
  static void showImagePreviewWithRequest(
    BuildContext context,
    Future<Response<Uint8List>> Function() request,
  ) {
    showDialog(
      context: context,
      barrierColor: Colors.black.withAlpha(180),
      builder: (BuildContext context) {
        return Stack(
          children: [
            InteractiveViewer(
              clipBehavior: Clip.none,
              child: ImageFromRequest(
                fit: BoxFit.contain,
                request: request,
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height,
                borderRadius: 10,
              ),
            ),
            Align(
              alignment: Alignment.topLeft,
              child: IconButton(
                onPressed: () {
                  context.router.maybePop();
                },
                icon: Icon(
                  CupertinoIcons.chevron_back,
                  blendMode: BlendMode.difference,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  static void showImagePreview(BuildContext context, Widget child) {
    showDialog(
      context: context,
      barrierColor: Colors.black.withAlpha(180),
      builder: (BuildContext context) {
        return Stack(
          children: [
            InteractiveViewer(
              clipBehavior: Clip.none,
              child: Center(child: child),
            ),
            Align(
              alignment: Alignment.topLeft,
              child: IconButton(
                onPressed: () {
                  context.router.maybePop();
                },
                icon: Icon(
                  CupertinoIcons.chevron_back,
                  blendMode: BlendMode.difference,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
