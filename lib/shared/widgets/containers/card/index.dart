import 'package:flutter/material.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';

class CustomCard extends StatelessWidget {
  const CustomCard({
    super.key,
    this.padding,
    this.onTap,
    this.child,
    this.onTapDown,
    this.onSecondaryTapDown,
    this.isLoading,
    this.border,
    this.color,
    this.constraints,
    this.height,
    this.margin,
    this.clipBehavior = Clip.hardEdge,
    this.borderRadius = 20.0,
  });

  final EdgeInsets? padding;
  final void Function()? onTap;
  final void Function(TapDownDetails)? onSecondaryTapDown;
  final void Function(TapDownDetails)? onTapDown;
  final Widget? child;
  final bool? isLoading;
  final BoxBorder? border;
  final Color? color;
  final BoxConstraints? constraints;
  final double? height;
  final EdgeInsets? margin;
  final Clip clipBehavior;
  final double borderRadius;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final innerColor =
        color ?? (isDarkTheme ? AppColors.darkSurface : AppColors.lightSurface);

    return Container(
      clipBehavior: clipBehavior,
      height: height,
      constraints: constraints,
      margin: margin,
      decoration: BoxDecoration(
        color: innerColor,
        borderRadius: BorderRadius.circular(borderRadius),
        border:
            border ??
            Border.all(
              color: isDarkTheme ? AppColors.darkStroke : AppColors.lightStroke,
            ),
        boxShadow: [
          // BoxShadow(
          //   offset: const Offset(0, 4),
          //   blurRadius: 12.0,
          //   color: AppColors.lightPrimary
          //       .withOpacity(isLoading != true ? 0.06 : 0),
          // ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: GestureDetector(
          onLongPressStart: (details) {
            onSecondaryTapDown?.call(
              TapDownDetails(globalPosition: details.globalPosition),
            );
          },
          child: InkWell(
            borderRadius: BorderRadius.circular(20.0),
            onTap: onTap,
            onTapDown: onTapDown,
            onSecondaryTapDown: onSecondaryTapDown,
            child: Ink(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.0),
              ),
              padding: padding ?? const EdgeInsets.all(12.0),
              child: child,
            ),
          ),
        ),
      ),
    );
  }
}
