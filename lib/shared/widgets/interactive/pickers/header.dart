import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';

class DurationPickerHeader extends StatelessWidget {
  final String title;
  final VoidCallback onConfirm;

  const DurationPickerHeader({
    super.key,
    required this.title,
    required this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const SizedBox(width: 12.0),
        Material(
          color: Colors.transparent,
          child: Text(title, style: Fonts.labelSmall),
        ),
        const Spacer(),
        CupertinoButton(onPressed: onConfirm, child: const Text('Готово')),
      ],
    );
  }
}
