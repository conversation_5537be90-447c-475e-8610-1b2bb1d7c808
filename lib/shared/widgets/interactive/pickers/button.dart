import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/interactive/pickers/index.dart';

class DurationPicker extends StatelessWidget {
  final Duration duration;
  final String label;
  final ValueChanged<Duration> onDurationChanged;
  final bool isDarkTheme;

  const DurationPicker({
    super.key,
    required this.duration,
    required this.label,
    required this.onDurationChanged,
    required this.isDarkTheme,
  });

  @override
  Widget build(BuildContext context) {
    final textColor = isDarkTheme ? Colors.white : Colors.black;
    final hintColor = Theme.of(context).hintColor;

    return InkWell(
      borderRadius: BorderRadius.circular(20),
      onTap:
          () =>
              _showDurationPicker(context, duration, onDurationChanged, label),
      child: InputDecorator(
        decoration: InputDecoration(labelText: label),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              duration.inDays > 0 || duration.inHours > 0
                  ? _formatDuration(duration)
                  : 'Выберите длительность',
              style: Fonts.labelSmall.merge(
                TextStyle(
                  color: duration != Duration.zero ? textColor : hintColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final days = duration.inDays;
    final hours = duration.inHours.remainder(24);

    if (days > 0 && hours > 0) return '$days дн. $hours ч.';
    if (days > 0) return '$days дн.';
    return '$hours ч.';
  }

  void _showDurationPicker(
    BuildContext context,
    Duration initialDuration,
    ValueChanged<Duration> onDurationChanged,
    String title,
  ) {
    int days = initialDuration.inDays;
    int hours = initialDuration.inHours.remainder(24);

    showCupertinoModalPopup(
      context: context,
      builder:
          (context) => DurationPickerDialog(
            title: title,
            initialDays: days,
            initialHours: hours,
            onConfirm: (d, h) => onDurationChanged(Duration(days: d, hours: h)),
          ),
    );
  }
}
