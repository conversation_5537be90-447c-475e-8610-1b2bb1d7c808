import 'package:flutter/cupertino.dart';

class DaysPicker extends StatelessWidget {
  final int initialValue;
  final ValueChanged<int> onChanged;

  const DaysPicker({
    super.key,
    required this.initialValue,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: CupertinoPicker(
        itemExtent: 40,
        useMagnifier: true,
        onSelectedItemChanged: onChanged,
        scrollController: FixedExtentScrollController(
          initialItem: initialValue,
        ),
        children: List.generate(366, (i) => Center(child: Text('$i дн.'))),
      ),
    );
  }
}

class HoursPicker extends StatelessWidget {
  final int initialValue;
  final ValueChanged<int> onChanged;

  const HoursPicker({
    super.key,
    required this.initialValue,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: CupertinoPicker(
        itemExtent: 40,
        useMagnifier: true,
        onSelectedItemChanged: onChanged,
        scrollController: FixedExtentScrollController(
          initialItem: initialValue,
        ),
        children: List.generate(24, (i) => Center(child: Text('$i ч.'))),
      ),
    );
  }
}
