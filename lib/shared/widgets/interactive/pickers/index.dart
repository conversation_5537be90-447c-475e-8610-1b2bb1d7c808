import 'package:flutter/material.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/widgets/interactive/pickers/header.dart';
import 'package:tony_clou_app/shared/widgets/interactive/pickers/pickers.dart';

class DurationPickerDialog extends StatefulWidget {
  final String title;
  final int initialDays;
  final int initialHours;
  final void Function(int days, int hours) onConfirm;

  const DurationPickerDialog({
    super.key,
    required this.title,
    required this.initialDays,
    required this.initialHours,
    required this.onConfirm,
  });

  @override
  State<DurationPickerDialog> createState() => _DurationPickerDialogState();
}

class _DurationPickerDialogState extends State<DurationPickerDialog> {
  late int _days;
  late int _hours;

  @override
  void initState() {
    super.initState();
    _days = widget.initialDays;
    _hours = widget.initialHours;
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Container(
      height: 300,
      width: double.infinity,
      color: isDarkTheme ? AppColors.darkBackground : AppColors.lightBackground,
      child: Column(
        children: [
          DurationPickerHeader(
            title: widget.title,
            onConfirm: () => widget.onConfirm(_days, _hours),
          ),
          Expanded(
            child: Row(
              children: [
                DaysPicker(
                  initialValue: _days,
                  onChanged: (value) => _days = value,
                ),
                HoursPicker(
                  initialValue: _hours,
                  onChanged: (value) => _hours = value,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
