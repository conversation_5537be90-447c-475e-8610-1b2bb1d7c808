import 'package:flutter/material.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';

enum CustomElevatedButtonTypes {
  common,
  accent,
  inverse,
  attention,
  success,
  coins,
  gems,
}

class CustomElevatedButton extends StatelessWidget {
  const CustomElevatedButton({
    super.key,
    required this.onPressed,
    this.text,
    this.child,
    this.type = CustomElevatedButtonTypes.common,
    this.disabled = false,
    this.style,
    this.mainAxisSize = MainAxisSize.max,
    this.childWithoutText = true,
  });

  final void Function() onPressed;
  final String? text;
  final Widget? child;
  final CustomElevatedButtonTypes type;
  final bool? disabled;
  final TextStyle? style;
  final MainAxisSize mainAxisSize;
  final bool childWithoutText;

  Color getBackgroundColor(
    CustomElevatedButtonTypes type,
    BuildContext context,
  ) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    switch (type) {
      case CustomElevatedButtonTypes.common:
        return isDarkTheme ? AppColors.darkSurface : AppColors.lightSurface;
      case CustomElevatedButtonTypes.inverse:
        return isDarkTheme ? AppColors.darkPrimary : AppColors.lightPrimary;
      case CustomElevatedButtonTypes.accent:
        return isDarkTheme ? AppColors.darkSecondary : AppColors.lightSecondary;
      case CustomElevatedButtonTypes.attention:
        return isDarkTheme ? AppColors.darkError : AppColors.lightError;
      case CustomElevatedButtonTypes.success:
        return isDarkTheme ? AppColors.darkSuccess : AppColors.lightSuccess;
      case CustomElevatedButtonTypes.coins:
        return isDarkTheme ? AppColors.darkCoin : AppColors.lightCoin;
      case CustomElevatedButtonTypes.gems:
        return isDarkTheme ? AppColors.darkGem : AppColors.darkGem;
    }
  }

  Color getFontColor(CustomElevatedButtonTypes type, BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    if (disabled ?? false) {
      return AppColors.medium;
    }
    switch (type) {
      case CustomElevatedButtonTypes.common:
        return isDarkTheme ? AppColors.darkPrimary : AppColors.lightPrimary;
      case CustomElevatedButtonTypes.inverse:
        return isDarkTheme
            ? AppColors.darkBackground
            : AppColors.lightBackground;
      case CustomElevatedButtonTypes.accent:
        return AppColors.lightBackground;
      case CustomElevatedButtonTypes.attention:
        return AppColors.lightBackground;
      case CustomElevatedButtonTypes.success:
        return AppColors.lightPrimary;
      case CustomElevatedButtonTypes.coins:
        return AppColors.lightBackground;
      case CustomElevatedButtonTypes.gems:
        return AppColors.lightBackground;
    }
  }

  @override
  Widget build(BuildContext context) {
    // final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    final fontColor = getFontColor(type, context);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(100000.0),
        // boxShadow: [
        //   BoxShadow(
        //     offset: const Offset(0, 4),
        //     blurRadius: 12.0,
        //     color: AppColors.lightPrimary.withValues(alpha: 0.06),
        //   ),
        // ],
        border: Border.all(color: fontColor.withValues(alpha: 0.12)),
      ),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          overlayColor: fontColor,
          disabledBackgroundColor: AppColors.medium.withValues(alpha: 0.2),
          backgroundColor: getBackgroundColor(type, context),
          minimumSize: Size(0, 48),
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
        onPressed: disabled ?? false ? null : onPressed,
        child: Row(
          mainAxisSize: mainAxisSize,
          mainAxisAlignment: MainAxisAlignment.center,
          spacing: 6.0,
          children: [
            if (child != null) child!,
            if (text != null && !(child != null && childWithoutText))
              Flexible(
                child: Text(
                  text!,
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: Fonts.labelMedium
                      .merge(TextStyle(color: fontColor))
                      .merge(style),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
