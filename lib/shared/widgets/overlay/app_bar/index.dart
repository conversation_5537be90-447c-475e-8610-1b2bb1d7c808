import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';

class CustomCupertinoNavigationBar extends StatefulWidget
    implements PreferredSizeWidget {
  const CustomCupertinoNavigationBar({
    super.key,
    this.rightPart,
    this.leftIcon,
    this.height = 44.0, // Стандартная высота для iOS
    this.isLoading = false,
    this.title,
    this.description,
    this.backgroundColor,
    this.automaticallyImplyLeading = true,
    this.border,
    this.padding,
    this.transitionBetweenRoutes = true,
  });

  final Widget? rightPart;
  final String? leftIcon;
  final String? title;
  final String? description;
  final Color? backgroundColor;
  final bool automaticallyImplyLeading;
  final Border? border;
  final EdgeInsetsDirectional? padding;
  final bool transitionBetweenRoutes;

  // utility
  final double height;
  final bool? isLoading;

  @override
  State<CustomCupertinoNavigationBar> createState() =>
      _CustomCupertinoNavigationBarState();

  @override
  Size get preferredSize => Size.fromHeight(height);
}

class _CustomCupertinoNavigationBarState
    extends State<CustomCupertinoNavigationBar> {
  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor =
        widget.backgroundColor ??
        (isDarkTheme ? AppColors.darkBackground : AppColors.lightBackground);

    final canPop = context.router.canPop();

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        border:
            widget.border ??
            Border(
              bottom: BorderSide(
                color:
                    isDarkTheme ? AppColors.darkStroke : AppColors.lightStroke,
                width: 0.5,
              ),
            ),
      ),
      child: SafeArea(
        bottom: false,
        child: Padding(
          padding:
              widget.padding ??
              const EdgeInsetsDirectional.symmetric(horizontal: 16.0),
          child: SizedBox(
            height: widget.height,
            child: NavigationToolbar(
              leading:
                  widget.automaticallyImplyLeading && canPop
                      ? CupertinoButton(
                        padding: EdgeInsets.zero,
                        onPressed: () => context.router.pop(),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              CupertinoIcons.back,
                              size: 28.0,
                              color:
                                  isDarkTheme
                                      ? AppColors.darkPrimary
                                      : AppColors.lightPrimary,
                            ),
                            const SizedBox(width: 4.0),
                            Text(
                              'Назад',
                              style: Fonts.labelMedium.copyWith(
                                color:
                                    isDarkTheme
                                        ? AppColors.darkPrimary
                                        : AppColors.lightPrimary,
                              ),
                            ),
                          ],
                        ),
                      )
                      : null,
              middle:
                  widget.title != null
                      ? Text(
                        widget.title!,
                        style: Fonts.titleMedium.copyWith(
                          fontWeight: FontWeight.w600,
                          color:
                              isDarkTheme
                                  ? AppColors.darkPrimary
                                  : AppColors.lightPrimary,
                        ),
                        textAlign: TextAlign.center,
                      )
                      : null,
              trailing: widget.rightPart,
              centerMiddle: true,
              middleSpacing: 16.0,
            ),
          ),
        ),
      ),
    );
  }
}
