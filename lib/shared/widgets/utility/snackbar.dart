import 'package:flutter/material.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';

enum SnackBarType { common, error }

class CustomSnackbar extends StatelessWidget {
  const CustomSnackbar({
    super.key,
    this.content,
    this.text,
    this.type = SnackBarType.common,
  });

  final Widget? content;
  final String? text;
  final SnackBarType type;

  Color getBackgroundColor(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    switch (type) {
      case SnackBarType.common:
        return isDarkTheme ? AppColors.darkPrimary : AppColors.lightPrimary;
      case SnackBarType.error:
        return isDarkTheme ? AppColors.darkError : AppColors.lightError;
    }
  }

  Color getTextColor(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    switch (type) {
      case SnackBarType.common:
        return isDarkTheme
            ? AppColors.darkBackground
            : AppColors.lightBackground;
      case SnackBarType.error:
        return isDarkTheme ? AppColors.darkPrimary : AppColors.lightBackground;
    }
  }

  /// Преобразует CustomSnackbar в стандартный SnackBar
  SnackBar toSnackBar(BuildContext context) {
    return SnackBar(
      elevation: 0.0,
      backgroundColor: Colors.transparent,
      padding: const EdgeInsets.all(12.0),
      content: Container(
        padding: const EdgeInsets.all(12.0),
        decoration: BoxDecoration(
          color: getBackgroundColor(context),
          borderRadius: BorderRadius.circular(20.0),
        ),
        child:
            content ??
            Text(
              text ?? 'Нужно ввести текст в SnackBar!',
              // style: Fonts.labelSmall.merge(
              //   TextStyle(color: getTextColor(context)),
              // ),
              textAlign: TextAlign.center,
            ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Вернуть что-то по умолчанию, если используется как виджет
    return toSnackBar(context);
  }
}
