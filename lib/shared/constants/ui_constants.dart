/// Общие UI константы для всего приложения
class UIConstants {
  // Размеры отступов
  static const double paddingTiny = 2.0;
  static const double paddingSmall = 4.0;
  static const double paddingMedium = 8.0;
  static const double paddingLarge = 12.0;
  static const double paddingXLarge = 16.0;
  static const double paddingXXLarge = 20.0;
  static const double paddingHuge = 24.0;
  static const double paddingMassive = 32.0;

  // Размеры иконок
  static const double iconSmall = 16.0;
  static const double iconMedium = 20.0;
  static const double iconLarge = 24.0;
  static const double iconXLarge = 32.0;
  static const double iconHuge = 40.0;

  // Радиусы скругления
  static const double radiusSmall = 4.0;
  static const double radiusMedium = 8.0;
  static const double radiusLarge = 12.0;
  static const double radiusXLarge = 16.0;
  static const double radiusXXLarge = 20.0;

  // Размеры границ
  static const double borderThin = 0.5;
  static const double borderMedium = 1.0;
  static const double borderThick = 2.0;

  // Прозрачность
  static const double opacityLight = 0.1;
  static const double opacityMedium = 0.3;
  static const double opacityHeavy = 0.5;

  // Высоты элементов
  static const double bannerHeight = 128.0;
  static const double cardMinHeight = 60.0;
  static const double buttonHeight = 44.0;

  // Приватный конструктор для предотвращения создания экземпляров
  UIConstants._();
}

/// Константы для анимаций
class AnimationConstants {
  static const Duration fast = Duration(milliseconds: 150);
  static const Duration medium = Duration(milliseconds: 300);
  static const Duration slow = Duration(milliseconds: 500);

  AnimationConstants._();
}

/// Константы для текстов приложения
class AppTexts {
  // Заголовки экранов
  static const String appTitle = 'Tony Clou';
  static const String locationsTitle = 'Где нас найти';
  static const String aboutTitle = 'О нас';
  static const String contactsTitle = 'Контакты';
  static const String balanceTitle = 'Баланс';
  static const String adminTitle = 'Админка';

  // Описания
  static const String balanceSubtitle = 'Просмотр баланса и истории';
  static const String locationsSubtitle = 'Адреса наших офисов';
  static const String aboutSubtitle = 'Информация о компании';
  static const String contactsSubtitle = 'Телефоны, почта, соцсети';
  static const String adminSubtitle = 'Панель администратора';

  // Общие тексты
  static const String ok = 'OK';
  static const String cancel = 'Отмена';
  static const String close = 'Закрыть';
  static const String save = 'Сохранить';
  static const String delete = 'Удалить';
  static const String edit = 'Редактировать';

  AppTexts._();
}
