import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';

part 'token_claims.freezed.dart';
part 'token_claims.g.dart';

@freezed
abstract class TokenClaims with _$TokenClaims {
  @JsonSerializable(includeIfNull: false)
  factory TokenClaims({
    String? sub,
    String? email,
    UserRole? role,
    TokenType? type,
    String? deviceId,
  }) = _TokenClaims;

  factory TokenClaims.fromJson(Map<String, dynamic> json) =>
      _$TokenClaimsFromJson(json);
}

enum TokenType {
  @JsonValue('access')
  access,
  @JsonValue('refresh')
  refresh,
}
