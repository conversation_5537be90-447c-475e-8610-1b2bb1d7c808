import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';

part 'index.freezed.dart';
part 'index.g.dart';

@freezed
abstract class EmailVerifyCodeOutput with _$EmailVerifyCodeOutput {
  @JsonSerializable(includeIfNull: false)
  factory EmailVerifyCodeOutput({
    String? accessToken,
    String? refreshToken,
    String? deviceId,
    User? user,
    String? message,
  }) = _EmailVerifyCodeOutput;

  factory EmailVerifyCodeOutput.fromJson(Map<String, dynamic> json) =>
      _$EmailVerifyCodeOutputFromJson(json);
}

@freezed
abstract class RefreshTokenOutput with _$RefreshTokenOutput {
  @JsonSerializable(includeIfNull: false)
  factory RefreshTokenOutput({
    String? accessToken,
    String? refreshToken,
    String? message,
  }) = _RefreshTokenOutput;

  factory RefreshTokenOutput.fromJson(Map<String, dynamic> json) =>
      _$RefreshTokenOutputFromJson(json);
}

@freezed
abstract class AlternativeSignInOutput with _$AlternativeSignInOutput {
  @JsonSerializable(includeIfNull: false)
  factory AlternativeSignInOutput({
    String? accessToken,
    String? refreshToken,
    String? deviceId,
    User? user,
    String? message,
  }) = _AlternativeSignInOutput;

  factory AlternativeSignInOutput.fromJson(Map<String, dynamic> json) =>
      _$AlternativeSignInOutputFromJson(json);
}
