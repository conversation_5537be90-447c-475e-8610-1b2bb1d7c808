// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'index.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$EmailVerifyCodeOutput {

 String? get accessToken; String? get refreshToken; String? get deviceId; User? get user; String? get message;
/// Create a copy of EmailVerifyCodeOutput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EmailVerifyCodeOutputCopyWith<EmailVerifyCodeOutput> get copyWith => _$EmailVerifyCodeOutputCopyWithImpl<EmailVerifyCodeOutput>(this as EmailVerifyCodeOutput, _$identity);

  /// Serializes this EmailVerifyCodeOutput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EmailVerifyCodeOutput&&(identical(other.accessToken, accessToken) || other.accessToken == accessToken)&&(identical(other.refreshToken, refreshToken) || other.refreshToken == refreshToken)&&(identical(other.deviceId, deviceId) || other.deviceId == deviceId)&&(identical(other.user, user) || other.user == user)&&(identical(other.message, message) || other.message == message));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accessToken,refreshToken,deviceId,user,message);

@override
String toString() {
  return 'EmailVerifyCodeOutput(accessToken: $accessToken, refreshToken: $refreshToken, deviceId: $deviceId, user: $user, message: $message)';
}


}

/// @nodoc
abstract mixin class $EmailVerifyCodeOutputCopyWith<$Res>  {
  factory $EmailVerifyCodeOutputCopyWith(EmailVerifyCodeOutput value, $Res Function(EmailVerifyCodeOutput) _then) = _$EmailVerifyCodeOutputCopyWithImpl;
@useResult
$Res call({
 String? accessToken, String? refreshToken, String? deviceId, User? user, String? message
});


$UserCopyWith<$Res>? get user;

}
/// @nodoc
class _$EmailVerifyCodeOutputCopyWithImpl<$Res>
    implements $EmailVerifyCodeOutputCopyWith<$Res> {
  _$EmailVerifyCodeOutputCopyWithImpl(this._self, this._then);

  final EmailVerifyCodeOutput _self;
  final $Res Function(EmailVerifyCodeOutput) _then;

/// Create a copy of EmailVerifyCodeOutput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accessToken = freezed,Object? refreshToken = freezed,Object? deviceId = freezed,Object? user = freezed,Object? message = freezed,}) {
  return _then(_self.copyWith(
accessToken: freezed == accessToken ? _self.accessToken : accessToken // ignore: cast_nullable_to_non_nullable
as String?,refreshToken: freezed == refreshToken ? _self.refreshToken : refreshToken // ignore: cast_nullable_to_non_nullable
as String?,deviceId: freezed == deviceId ? _self.deviceId : deviceId // ignore: cast_nullable_to_non_nullable
as String?,user: freezed == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as User?,message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of EmailVerifyCodeOutput
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserCopyWith<$Res>? get user {
    if (_self.user == null) {
    return null;
  }

  return $UserCopyWith<$Res>(_self.user!, (value) {
    return _then(_self.copyWith(user: value));
  });
}
}


/// @nodoc

@JsonSerializable(includeIfNull: false)
class _EmailVerifyCodeOutput implements EmailVerifyCodeOutput {
   _EmailVerifyCodeOutput({this.accessToken, this.refreshToken, this.deviceId, this.user, this.message});
  factory _EmailVerifyCodeOutput.fromJson(Map<String, dynamic> json) => _$EmailVerifyCodeOutputFromJson(json);

@override final  String? accessToken;
@override final  String? refreshToken;
@override final  String? deviceId;
@override final  User? user;
@override final  String? message;

/// Create a copy of EmailVerifyCodeOutput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EmailVerifyCodeOutputCopyWith<_EmailVerifyCodeOutput> get copyWith => __$EmailVerifyCodeOutputCopyWithImpl<_EmailVerifyCodeOutput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$EmailVerifyCodeOutputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EmailVerifyCodeOutput&&(identical(other.accessToken, accessToken) || other.accessToken == accessToken)&&(identical(other.refreshToken, refreshToken) || other.refreshToken == refreshToken)&&(identical(other.deviceId, deviceId) || other.deviceId == deviceId)&&(identical(other.user, user) || other.user == user)&&(identical(other.message, message) || other.message == message));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accessToken,refreshToken,deviceId,user,message);

@override
String toString() {
  return 'EmailVerifyCodeOutput(accessToken: $accessToken, refreshToken: $refreshToken, deviceId: $deviceId, user: $user, message: $message)';
}


}

/// @nodoc
abstract mixin class _$EmailVerifyCodeOutputCopyWith<$Res> implements $EmailVerifyCodeOutputCopyWith<$Res> {
  factory _$EmailVerifyCodeOutputCopyWith(_EmailVerifyCodeOutput value, $Res Function(_EmailVerifyCodeOutput) _then) = __$EmailVerifyCodeOutputCopyWithImpl;
@override @useResult
$Res call({
 String? accessToken, String? refreshToken, String? deviceId, User? user, String? message
});


@override $UserCopyWith<$Res>? get user;

}
/// @nodoc
class __$EmailVerifyCodeOutputCopyWithImpl<$Res>
    implements _$EmailVerifyCodeOutputCopyWith<$Res> {
  __$EmailVerifyCodeOutputCopyWithImpl(this._self, this._then);

  final _EmailVerifyCodeOutput _self;
  final $Res Function(_EmailVerifyCodeOutput) _then;

/// Create a copy of EmailVerifyCodeOutput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accessToken = freezed,Object? refreshToken = freezed,Object? deviceId = freezed,Object? user = freezed,Object? message = freezed,}) {
  return _then(_EmailVerifyCodeOutput(
accessToken: freezed == accessToken ? _self.accessToken : accessToken // ignore: cast_nullable_to_non_nullable
as String?,refreshToken: freezed == refreshToken ? _self.refreshToken : refreshToken // ignore: cast_nullable_to_non_nullable
as String?,deviceId: freezed == deviceId ? _self.deviceId : deviceId // ignore: cast_nullable_to_non_nullable
as String?,user: freezed == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as User?,message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of EmailVerifyCodeOutput
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserCopyWith<$Res>? get user {
    if (_self.user == null) {
    return null;
  }

  return $UserCopyWith<$Res>(_self.user!, (value) {
    return _then(_self.copyWith(user: value));
  });
}
}


/// @nodoc
mixin _$RefreshTokenOutput {

 String? get accessToken; String? get refreshToken; String? get message;
/// Create a copy of RefreshTokenOutput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RefreshTokenOutputCopyWith<RefreshTokenOutput> get copyWith => _$RefreshTokenOutputCopyWithImpl<RefreshTokenOutput>(this as RefreshTokenOutput, _$identity);

  /// Serializes this RefreshTokenOutput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RefreshTokenOutput&&(identical(other.accessToken, accessToken) || other.accessToken == accessToken)&&(identical(other.refreshToken, refreshToken) || other.refreshToken == refreshToken)&&(identical(other.message, message) || other.message == message));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accessToken,refreshToken,message);

@override
String toString() {
  return 'RefreshTokenOutput(accessToken: $accessToken, refreshToken: $refreshToken, message: $message)';
}


}

/// @nodoc
abstract mixin class $RefreshTokenOutputCopyWith<$Res>  {
  factory $RefreshTokenOutputCopyWith(RefreshTokenOutput value, $Res Function(RefreshTokenOutput) _then) = _$RefreshTokenOutputCopyWithImpl;
@useResult
$Res call({
 String? accessToken, String? refreshToken, String? message
});




}
/// @nodoc
class _$RefreshTokenOutputCopyWithImpl<$Res>
    implements $RefreshTokenOutputCopyWith<$Res> {
  _$RefreshTokenOutputCopyWithImpl(this._self, this._then);

  final RefreshTokenOutput _self;
  final $Res Function(RefreshTokenOutput) _then;

/// Create a copy of RefreshTokenOutput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accessToken = freezed,Object? refreshToken = freezed,Object? message = freezed,}) {
  return _then(_self.copyWith(
accessToken: freezed == accessToken ? _self.accessToken : accessToken // ignore: cast_nullable_to_non_nullable
as String?,refreshToken: freezed == refreshToken ? _self.refreshToken : refreshToken // ignore: cast_nullable_to_non_nullable
as String?,message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc

@JsonSerializable(includeIfNull: false)
class _RefreshTokenOutput implements RefreshTokenOutput {
   _RefreshTokenOutput({this.accessToken, this.refreshToken, this.message});
  factory _RefreshTokenOutput.fromJson(Map<String, dynamic> json) => _$RefreshTokenOutputFromJson(json);

@override final  String? accessToken;
@override final  String? refreshToken;
@override final  String? message;

/// Create a copy of RefreshTokenOutput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RefreshTokenOutputCopyWith<_RefreshTokenOutput> get copyWith => __$RefreshTokenOutputCopyWithImpl<_RefreshTokenOutput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$RefreshTokenOutputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RefreshTokenOutput&&(identical(other.accessToken, accessToken) || other.accessToken == accessToken)&&(identical(other.refreshToken, refreshToken) || other.refreshToken == refreshToken)&&(identical(other.message, message) || other.message == message));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accessToken,refreshToken,message);

@override
String toString() {
  return 'RefreshTokenOutput(accessToken: $accessToken, refreshToken: $refreshToken, message: $message)';
}


}

/// @nodoc
abstract mixin class _$RefreshTokenOutputCopyWith<$Res> implements $RefreshTokenOutputCopyWith<$Res> {
  factory _$RefreshTokenOutputCopyWith(_RefreshTokenOutput value, $Res Function(_RefreshTokenOutput) _then) = __$RefreshTokenOutputCopyWithImpl;
@override @useResult
$Res call({
 String? accessToken, String? refreshToken, String? message
});




}
/// @nodoc
class __$RefreshTokenOutputCopyWithImpl<$Res>
    implements _$RefreshTokenOutputCopyWith<$Res> {
  __$RefreshTokenOutputCopyWithImpl(this._self, this._then);

  final _RefreshTokenOutput _self;
  final $Res Function(_RefreshTokenOutput) _then;

/// Create a copy of RefreshTokenOutput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accessToken = freezed,Object? refreshToken = freezed,Object? message = freezed,}) {
  return _then(_RefreshTokenOutput(
accessToken: freezed == accessToken ? _self.accessToken : accessToken // ignore: cast_nullable_to_non_nullable
as String?,refreshToken: freezed == refreshToken ? _self.refreshToken : refreshToken // ignore: cast_nullable_to_non_nullable
as String?,message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$AlternativeSignInOutput {

 String? get accessToken; String? get refreshToken; String? get deviceId; User? get user; String? get message;
/// Create a copy of AlternativeSignInOutput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AlternativeSignInOutputCopyWith<AlternativeSignInOutput> get copyWith => _$AlternativeSignInOutputCopyWithImpl<AlternativeSignInOutput>(this as AlternativeSignInOutput, _$identity);

  /// Serializes this AlternativeSignInOutput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AlternativeSignInOutput&&(identical(other.accessToken, accessToken) || other.accessToken == accessToken)&&(identical(other.refreshToken, refreshToken) || other.refreshToken == refreshToken)&&(identical(other.deviceId, deviceId) || other.deviceId == deviceId)&&(identical(other.user, user) || other.user == user)&&(identical(other.message, message) || other.message == message));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accessToken,refreshToken,deviceId,user,message);

@override
String toString() {
  return 'AlternativeSignInOutput(accessToken: $accessToken, refreshToken: $refreshToken, deviceId: $deviceId, user: $user, message: $message)';
}


}

/// @nodoc
abstract mixin class $AlternativeSignInOutputCopyWith<$Res>  {
  factory $AlternativeSignInOutputCopyWith(AlternativeSignInOutput value, $Res Function(AlternativeSignInOutput) _then) = _$AlternativeSignInOutputCopyWithImpl;
@useResult
$Res call({
 String? accessToken, String? refreshToken, String? deviceId, User? user, String? message
});


$UserCopyWith<$Res>? get user;

}
/// @nodoc
class _$AlternativeSignInOutputCopyWithImpl<$Res>
    implements $AlternativeSignInOutputCopyWith<$Res> {
  _$AlternativeSignInOutputCopyWithImpl(this._self, this._then);

  final AlternativeSignInOutput _self;
  final $Res Function(AlternativeSignInOutput) _then;

/// Create a copy of AlternativeSignInOutput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accessToken = freezed,Object? refreshToken = freezed,Object? deviceId = freezed,Object? user = freezed,Object? message = freezed,}) {
  return _then(_self.copyWith(
accessToken: freezed == accessToken ? _self.accessToken : accessToken // ignore: cast_nullable_to_non_nullable
as String?,refreshToken: freezed == refreshToken ? _self.refreshToken : refreshToken // ignore: cast_nullable_to_non_nullable
as String?,deviceId: freezed == deviceId ? _self.deviceId : deviceId // ignore: cast_nullable_to_non_nullable
as String?,user: freezed == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as User?,message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of AlternativeSignInOutput
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserCopyWith<$Res>? get user {
    if (_self.user == null) {
    return null;
  }

  return $UserCopyWith<$Res>(_self.user!, (value) {
    return _then(_self.copyWith(user: value));
  });
}
}


/// @nodoc

@JsonSerializable(includeIfNull: false)
class _AlternativeSignInOutput implements AlternativeSignInOutput {
   _AlternativeSignInOutput({this.accessToken, this.refreshToken, this.deviceId, this.user, this.message});
  factory _AlternativeSignInOutput.fromJson(Map<String, dynamic> json) => _$AlternativeSignInOutputFromJson(json);

@override final  String? accessToken;
@override final  String? refreshToken;
@override final  String? deviceId;
@override final  User? user;
@override final  String? message;

/// Create a copy of AlternativeSignInOutput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AlternativeSignInOutputCopyWith<_AlternativeSignInOutput> get copyWith => __$AlternativeSignInOutputCopyWithImpl<_AlternativeSignInOutput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AlternativeSignInOutputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AlternativeSignInOutput&&(identical(other.accessToken, accessToken) || other.accessToken == accessToken)&&(identical(other.refreshToken, refreshToken) || other.refreshToken == refreshToken)&&(identical(other.deviceId, deviceId) || other.deviceId == deviceId)&&(identical(other.user, user) || other.user == user)&&(identical(other.message, message) || other.message == message));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accessToken,refreshToken,deviceId,user,message);

@override
String toString() {
  return 'AlternativeSignInOutput(accessToken: $accessToken, refreshToken: $refreshToken, deviceId: $deviceId, user: $user, message: $message)';
}


}

/// @nodoc
abstract mixin class _$AlternativeSignInOutputCopyWith<$Res> implements $AlternativeSignInOutputCopyWith<$Res> {
  factory _$AlternativeSignInOutputCopyWith(_AlternativeSignInOutput value, $Res Function(_AlternativeSignInOutput) _then) = __$AlternativeSignInOutputCopyWithImpl;
@override @useResult
$Res call({
 String? accessToken, String? refreshToken, String? deviceId, User? user, String? message
});


@override $UserCopyWith<$Res>? get user;

}
/// @nodoc
class __$AlternativeSignInOutputCopyWithImpl<$Res>
    implements _$AlternativeSignInOutputCopyWith<$Res> {
  __$AlternativeSignInOutputCopyWithImpl(this._self, this._then);

  final _AlternativeSignInOutput _self;
  final $Res Function(_AlternativeSignInOutput) _then;

/// Create a copy of AlternativeSignInOutput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accessToken = freezed,Object? refreshToken = freezed,Object? deviceId = freezed,Object? user = freezed,Object? message = freezed,}) {
  return _then(_AlternativeSignInOutput(
accessToken: freezed == accessToken ? _self.accessToken : accessToken // ignore: cast_nullable_to_non_nullable
as String?,refreshToken: freezed == refreshToken ? _self.refreshToken : refreshToken // ignore: cast_nullable_to_non_nullable
as String?,deviceId: freezed == deviceId ? _self.deviceId : deviceId // ignore: cast_nullable_to_non_nullable
as String?,user: freezed == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as User?,message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of AlternativeSignInOutput
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserCopyWith<$Res>? get user {
    if (_self.user == null) {
    return null;
  }

  return $UserCopyWith<$Res>(_self.user!, (value) {
    return _then(_self.copyWith(user: value));
  });
}
}

// dart format on
