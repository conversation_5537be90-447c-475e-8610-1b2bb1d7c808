// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'index.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_EmailVerifyCodeOutput _$EmailVerifyCodeOutputFromJson(
  Map<String, dynamic> json,
) => _EmailVerifyCodeOutput(
  accessToken: json['accessToken'] as String?,
  refreshToken: json['refreshToken'] as String?,
  deviceId: json['deviceId'] as String?,
  user:
      json['user'] == null
          ? null
          : User.from<PERSON>son(json['user'] as Map<String, dynamic>),
  message: json['message'] as String?,
);

Map<String, dynamic> _$EmailVerifyCodeOutputToJson(
  _EmailVerifyCodeOutput instance,
) => <String, dynamic>{
  if (instance.accessToken case final value?) 'accessToken': value,
  if (instance.refreshToken case final value?) 'refreshToken': value,
  if (instance.deviceId case final value?) 'deviceId': value,
  if (instance.user case final value?) 'user': value,
  if (instance.message case final value?) 'message': value,
};

_RefreshTokenOutput _$RefreshTokenOutputFromJson(Map<String, dynamic> json) =>
    _RefreshTokenOutput(
      accessToken: json['accessToken'] as String?,
      refreshToken: json['refreshToken'] as String?,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$RefreshTokenOutputToJson(_RefreshTokenOutput instance) =>
    <String, dynamic>{
      if (instance.accessToken case final value?) 'accessToken': value,
      if (instance.refreshToken case final value?) 'refreshToken': value,
      if (instance.message case final value?) 'message': value,
    };

_AlternativeSignInOutput _$AlternativeSignInOutputFromJson(
  Map<String, dynamic> json,
) => _AlternativeSignInOutput(
  accessToken: json['accessToken'] as String?,
  refreshToken: json['refreshToken'] as String?,
  deviceId: json['deviceId'] as String?,
  user:
      json['user'] == null
          ? null
          : User.fromJson(json['user'] as Map<String, dynamic>),
  message: json['message'] as String?,
);

Map<String, dynamic> _$AlternativeSignInOutputToJson(
  _AlternativeSignInOutput instance,
) => <String, dynamic>{
  if (instance.accessToken case final value?) 'accessToken': value,
  if (instance.refreshToken case final value?) 'refreshToken': value,
  if (instance.deviceId case final value?) 'deviceId': value,
  if (instance.user case final value?) 'user': value,
  if (instance.message case final value?) 'message': value,
};
