// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'token_claims.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TokenClaims _$TokenClaimsFromJson(Map<String, dynamic> json) => _TokenClaims(
  sub: json['sub'] as String?,
  email: json['email'] as String?,
  role: $enumDecodeNullable(_$UserRoleEnumMap, json['role']),
  type: $enumDecodeNullable(_$TokenTypeEnumMap, json['type']),
  deviceId: json['deviceId'] as String?,
);

Map<String, dynamic> _$TokenClaimsToJson(_TokenClaims instance) =>
    <String, dynamic>{
      if (instance.sub case final value?) 'sub': value,
      if (instance.email case final value?) 'email': value,
      if (_$UserRoleEnumMap[instance.role] case final value?) 'role': value,
      if (_$TokenTypeEnumMap[instance.type] case final value?) 'type': value,
      if (instance.deviceId case final value?) 'deviceId': value,
    };

const _$UserRoleEnumMap = {UserRole.admin: 'admin', UserRole.user: 'user'};

const _$TokenTypeEnumMap = {
  TokenType.access: 'access',
  TokenType.refresh: 'refresh',
};
