// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'token_claims.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TokenClaims {

 String? get sub; String? get email; UserRole? get role; TokenType? get type; String? get deviceId;
/// Create a copy of TokenClaims
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TokenClaimsCopyWith<TokenClaims> get copyWith => _$TokenClaimsCopyWithImpl<TokenClaims>(this as TokenClaims, _$identity);

  /// Serializes this TokenClaims to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TokenClaims&&(identical(other.sub, sub) || other.sub == sub)&&(identical(other.email, email) || other.email == email)&&(identical(other.role, role) || other.role == role)&&(identical(other.type, type) || other.type == type)&&(identical(other.deviceId, deviceId) || other.deviceId == deviceId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,sub,email,role,type,deviceId);

@override
String toString() {
  return 'TokenClaims(sub: $sub, email: $email, role: $role, type: $type, deviceId: $deviceId)';
}


}

/// @nodoc
abstract mixin class $TokenClaimsCopyWith<$Res>  {
  factory $TokenClaimsCopyWith(TokenClaims value, $Res Function(TokenClaims) _then) = _$TokenClaimsCopyWithImpl;
@useResult
$Res call({
 String? sub, String? email, UserRole? role, TokenType? type, String? deviceId
});




}
/// @nodoc
class _$TokenClaimsCopyWithImpl<$Res>
    implements $TokenClaimsCopyWith<$Res> {
  _$TokenClaimsCopyWithImpl(this._self, this._then);

  final TokenClaims _self;
  final $Res Function(TokenClaims) _then;

/// Create a copy of TokenClaims
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? sub = freezed,Object? email = freezed,Object? role = freezed,Object? type = freezed,Object? deviceId = freezed,}) {
  return _then(_self.copyWith(
sub: freezed == sub ? _self.sub : sub // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,role: freezed == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as UserRole?,type: freezed == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as TokenType?,deviceId: freezed == deviceId ? _self.deviceId : deviceId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc

@JsonSerializable(includeIfNull: false)
class _TokenClaims implements TokenClaims {
   _TokenClaims({this.sub, this.email, this.role, this.type, this.deviceId});
  factory _TokenClaims.fromJson(Map<String, dynamic> json) => _$TokenClaimsFromJson(json);

@override final  String? sub;
@override final  String? email;
@override final  UserRole? role;
@override final  TokenType? type;
@override final  String? deviceId;

/// Create a copy of TokenClaims
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TokenClaimsCopyWith<_TokenClaims> get copyWith => __$TokenClaimsCopyWithImpl<_TokenClaims>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TokenClaimsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TokenClaims&&(identical(other.sub, sub) || other.sub == sub)&&(identical(other.email, email) || other.email == email)&&(identical(other.role, role) || other.role == role)&&(identical(other.type, type) || other.type == type)&&(identical(other.deviceId, deviceId) || other.deviceId == deviceId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,sub,email,role,type,deviceId);

@override
String toString() {
  return 'TokenClaims(sub: $sub, email: $email, role: $role, type: $type, deviceId: $deviceId)';
}


}

/// @nodoc
abstract mixin class _$TokenClaimsCopyWith<$Res> implements $TokenClaimsCopyWith<$Res> {
  factory _$TokenClaimsCopyWith(_TokenClaims value, $Res Function(_TokenClaims) _then) = __$TokenClaimsCopyWithImpl;
@override @useResult
$Res call({
 String? sub, String? email, UserRole? role, TokenType? type, String? deviceId
});




}
/// @nodoc
class __$TokenClaimsCopyWithImpl<$Res>
    implements _$TokenClaimsCopyWith<$Res> {
  __$TokenClaimsCopyWithImpl(this._self, this._then);

  final _TokenClaims _self;
  final $Res Function(_TokenClaims) _then;

/// Create a copy of TokenClaims
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? sub = freezed,Object? email = freezed,Object? role = freezed,Object? type = freezed,Object? deviceId = freezed,}) {
  return _then(_TokenClaims(
sub: freezed == sub ? _self.sub : sub // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,role: freezed == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as UserRole?,type: freezed == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as TokenType?,deviceId: freezed == deviceId ? _self.deviceId : deviceId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
