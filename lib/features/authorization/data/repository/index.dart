import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:tony_clou_app/features/authorization/data/models/index.dart';
import 'package:tony_clou_app/shared/data/api.dart';

class AuthRepository {
  static Future<Response<dynamic>> emailVerify({required String email}) async {
    final body = {'email': email};

    final request = await API.request(
      url: '/auth/emailVerify',
      body: jsonEncode(body),
      method: 'POST',
    );

    return request;
  }

  static Future<Response<EmailVerifyCodeOutput>> emailVerifyCode({
    required String email,
    required String code,
  }) async {
    final body = {'email': email, 'code': code};

    final request = await API.request<EmailVerifyCodeOutput>(
      url: '/auth/emailVerifyCode',
      body: jsonEncode(body),
      fromJson: EmailVerifyCodeOutput.fromJson,
      method: 'POST',
    );
    final userId = request.data?.user?.id;
    if (userId != null && !kIsWeb) {
      await OneSignal.login(userId);
      print('OneSignal user ID установлен: $userId');
    }

    return request;
  }

  static Future<Response<RefreshTokenOutput>> refreshToken({
    required String refreshToken,
    required String deviceId,
  }) async {
    final body = {'refreshToken': refreshToken, 'deviceId': deviceId};

    final request = await API.request<RefreshTokenOutput>(
      url: '/auth/refreshToken',
      body: jsonEncode(body),
      method: 'POST',
      fromJson: RefreshTokenOutput.fromJson,
    );

    return request;
  }

  static Future<Response<dynamic>> logout() async {
    final body = {};

    final request = await API.request<dynamic>(
      url: '/auth/logout',
      body: jsonEncode(body),
      method: 'POST',
    );

    if (!kIsWeb) {
      await OneSignal.logout();
    }

    return request;
  }

  static Future<Response<dynamic>> logoutAll() async {
    final body = {};

    final request = await API.request<dynamic>(
      url: '/auth/logoutAll',
      body: jsonEncode(body),
      method: 'POST',
    );

    return request;
  }

  static Future<Response<AlternativeSignInOutput>> alternativeSignIn(
    String login,
    String password,
  ) async {
    final body = {'login': login, 'password': password};

    final request = await API.request<AlternativeSignInOutput>(
      url: '/auth/authByLogin',
      body: jsonEncode(body),
      method: 'POST',
      fromJson: AlternativeSignInOutput.fromJson,
    );
    final userId = request.data?.user?.id;
    if (userId != null && !kIsWeb) {
      await OneSignal.login(userId);
      print('OneSignal user ID установлен: $userId');
    }

    return request;
  }
}
