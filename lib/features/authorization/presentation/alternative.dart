import 'dart:convert';

import 'package:auto_route/auto_route.dart';
import 'package:crypto/crypto.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:tony_clou_app/core/navigation/index.gr.dart';
import 'package:tony_clou_app/features/authorization/data/repository/index.dart';
import 'package:tony_clou_app/features/main/presentation/bloc/user/index.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/wrapper/index.dart';
import 'package:tony_clou_app/shared/widgets/interactive/elevated_button.dart';

@RoutePage()
class AlternativeSignInScreen extends StatefulWidget {
  const AlternativeSignInScreen({super.key});

  @override
  State<AlternativeSignInScreen> createState() =>
      _AlternativeSignInScreenState();
}

class _AlternativeSignInScreenState extends State<AlternativeSignInScreen> {
  bool _isLoading = false;
  final TextEditingController _loginController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  void _signIn() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      final result = await AuthRepository.alternativeSignIn(
        _loginController.text,
        sha256.convert(utf8.encode(_passwordController.text)).toString(),
      );

      setState(() {
        _isLoading = false;
      });

      if (result.data?.user != null && mounted) {
        // user data
        context.read<BlocUser>().add(SetSelf(result.data!.user!));

        // access and refresh tokens
        final prefs = FlutterSecureStorage();
        prefs.write(key: 'accessToken', value: result.data?.accessToken);
        prefs.write(key: 'refreshToken', value: result.data?.refreshToken);
        prefs.write(key: 'deviceId', value: result.data?.deviceId);

        // redirect
        context.router.push(MainRoute());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Wrapper(
      appBar: CupertinoNavigationBar(
        previousPageTitle: '',
        middle: Text('Авторизация (login)', style: Fonts.labelMedium),
      ),
      body: Padding(
        padding: EdgeInsets.all(12.0),
        child: Column(
          children: [
            Form(
              key: _formKey,
              child: Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    TextFormField(
                      controller: _loginController,
                      decoration: InputDecoration(labelText: 'Логин'),
                      style: Fonts.labelSmall,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Пожалуйста, введите логин';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 16.0),
                    TextFormField(
                      controller: _passwordController,
                      decoration: InputDecoration(labelText: 'Пароль'),
                      style: Fonts.labelSmall,
                      obscureText: true,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Пожалуйста, введите пароль';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 32.0),
                    CustomElevatedButton(
                      onPressed: _signIn,
                      text: 'Войти',
                      child: _isLoading ? CupertinoActivityIndicator() : null,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
