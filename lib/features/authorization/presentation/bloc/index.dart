import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'events.dart';
part 'index.freezed.dart';
part 'state.dart';

class BlocAuthorization
    extends Bloc<BlocAuthorizationEvents, BlocAuthorizationState> {
  BlocAuthorization() : super(const BlocAuthorizationState()) {
    on<SetAuthEmail>((event, emit) {
      emit(state.copyWith(email: event.email));
    });
    on<SetAuthName>((event, emit) {
      emit(state.copyWith(name: event.name));
    });
    on<SetAuthSurname>((event, emit) {
      emit(state.copyWith(surname: event.surname));
    });
    on<SetAuthDateOfBirth>((event, emit) {
      emit(state.copyWith(dateOfBirth: event.dateOfBirth));
    });
    on<SetAuthPhoneNumber>((event, emit) {
      emit(state.copyWith(phoneNumber: event.phoneNumber));
    });
    on<ClearAuth>((event, emit) {
      emit(
        state.copyWith(
          email: null,
          name: null,
          surname: null,
          dateOfBirth: null,
          phoneNumber: null,
        ),
      );
    });
  }
}
