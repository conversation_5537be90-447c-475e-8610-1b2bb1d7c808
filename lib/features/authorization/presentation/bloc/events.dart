part of 'index.dart';

sealed class BlocAuthorizationEvents {}

final class SetAuthEmail extends BlocAuthorizationEvents {
  String email;
  SetAuthEmail(this.email);
}

final class SetAuthName extends BlocAuthorizationEvents {
  String name;
  SetAuthName(this.name);
}

final class SetAuthSurname extends BlocAuthorizationEvents {
  String surname;
  SetAuthSurname(this.surname);
}

final class SetAuthDateOfBirth extends BlocAuthorizationEvents {
  DateTime dateOfBirth;
  SetAuthDateOfBirth(this.dateOfBirth);
}

final class SetAuthPhoneNumber extends BlocAuthorizationEvents {
  String phoneNumber;
  SetAuthPhoneNumber(this.phoneNumber);
}

final class ClearAuth extends BlocAuthorizationEvents {
  ClearAuth();
}
