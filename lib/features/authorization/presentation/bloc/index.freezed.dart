// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'index.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$BlocAuthorizationState {

 String? get email; String? get name; String? get surname; DateTime? get dateOfBirth; String? get phoneNumber;
/// Create a copy of BlocAuthorizationState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BlocAuthorizationStateCopyWith<BlocAuthorizationState> get copyWith => _$BlocAuthorizationStateCopyWithImpl<BlocAuthorizationState>(this as BlocAuthorizationState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BlocAuthorizationState&&(identical(other.email, email) || other.email == email)&&(identical(other.name, name) || other.name == name)&&(identical(other.surname, surname) || other.surname == surname)&&(identical(other.dateOfBirth, dateOfBirth) || other.dateOfBirth == dateOfBirth)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber));
}


@override
int get hashCode => Object.hash(runtimeType,email,name,surname,dateOfBirth,phoneNumber);

@override
String toString() {
  return 'BlocAuthorizationState(email: $email, name: $name, surname: $surname, dateOfBirth: $dateOfBirth, phoneNumber: $phoneNumber)';
}


}

/// @nodoc
abstract mixin class $BlocAuthorizationStateCopyWith<$Res>  {
  factory $BlocAuthorizationStateCopyWith(BlocAuthorizationState value, $Res Function(BlocAuthorizationState) _then) = _$BlocAuthorizationStateCopyWithImpl;
@useResult
$Res call({
 String? email, String? name, String? surname, DateTime? dateOfBirth, String? phoneNumber
});




}
/// @nodoc
class _$BlocAuthorizationStateCopyWithImpl<$Res>
    implements $BlocAuthorizationStateCopyWith<$Res> {
  _$BlocAuthorizationStateCopyWithImpl(this._self, this._then);

  final BlocAuthorizationState _self;
  final $Res Function(BlocAuthorizationState) _then;

/// Create a copy of BlocAuthorizationState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? email = freezed,Object? name = freezed,Object? surname = freezed,Object? dateOfBirth = freezed,Object? phoneNumber = freezed,}) {
  return _then(_self.copyWith(
email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,surname: freezed == surname ? _self.surname : surname // ignore: cast_nullable_to_non_nullable
as String?,dateOfBirth: freezed == dateOfBirth ? _self.dateOfBirth : dateOfBirth // ignore: cast_nullable_to_non_nullable
as DateTime?,phoneNumber: freezed == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc


class _BlocAuthorizationState extends BlocAuthorizationState {
  const _BlocAuthorizationState({this.email, this.name, this.surname, this.dateOfBirth, this.phoneNumber}): super._();
  

@override final  String? email;
@override final  String? name;
@override final  String? surname;
@override final  DateTime? dateOfBirth;
@override final  String? phoneNumber;

/// Create a copy of BlocAuthorizationState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BlocAuthorizationStateCopyWith<_BlocAuthorizationState> get copyWith => __$BlocAuthorizationStateCopyWithImpl<_BlocAuthorizationState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BlocAuthorizationState&&(identical(other.email, email) || other.email == email)&&(identical(other.name, name) || other.name == name)&&(identical(other.surname, surname) || other.surname == surname)&&(identical(other.dateOfBirth, dateOfBirth) || other.dateOfBirth == dateOfBirth)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber));
}


@override
int get hashCode => Object.hash(runtimeType,email,name,surname,dateOfBirth,phoneNumber);

@override
String toString() {
  return 'BlocAuthorizationState(email: $email, name: $name, surname: $surname, dateOfBirth: $dateOfBirth, phoneNumber: $phoneNumber)';
}


}

/// @nodoc
abstract mixin class _$BlocAuthorizationStateCopyWith<$Res> implements $BlocAuthorizationStateCopyWith<$Res> {
  factory _$BlocAuthorizationStateCopyWith(_BlocAuthorizationState value, $Res Function(_BlocAuthorizationState) _then) = __$BlocAuthorizationStateCopyWithImpl;
@override @useResult
$Res call({
 String? email, String? name, String? surname, DateTime? dateOfBirth, String? phoneNumber
});




}
/// @nodoc
class __$BlocAuthorizationStateCopyWithImpl<$Res>
    implements _$BlocAuthorizationStateCopyWith<$Res> {
  __$BlocAuthorizationStateCopyWithImpl(this._self, this._then);

  final _BlocAuthorizationState _self;
  final $Res Function(_BlocAuthorizationState) _then;

/// Create a copy of BlocAuthorizationState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? email = freezed,Object? name = freezed,Object? surname = freezed,Object? dateOfBirth = freezed,Object? phoneNumber = freezed,}) {
  return _then(_BlocAuthorizationState(
email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,surname: freezed == surname ? _self.surname : surname // ignore: cast_nullable_to_non_nullable
as String?,dateOfBirth: freezed == dateOfBirth ? _self.dateOfBirth : dateOfBirth // ignore: cast_nullable_to_non_nullable
as DateTime?,phoneNumber: freezed == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
