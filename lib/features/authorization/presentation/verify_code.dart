import 'package:auto_route/auto_route.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:tony_clou_app/core/navigation/index.gr.dart';
import 'package:tony_clou_app/features/authorization/data/models/index.dart';
import 'package:tony_clou_app/features/authorization/data/repository/index.dart';
import 'package:tony_clou_app/features/authorization/presentation/bloc/index.dart';
import 'package:tony_clou_app/features/main/presentation/bloc/user/index.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/wrapper/index.dart';
import 'package:tony_clou_app/shared/widgets/interactive/elevated_button.dart';

@RoutePage()
class VerifyCodeScreen extends StatefulWidget {
  const VerifyCodeScreen({super.key});

  @override
  State<VerifyCodeScreen> createState() => _VerifyCodeScreenState();
}

class _VerifyCodeScreenState extends State<VerifyCodeScreen> {
  bool _isLoading = false;
  final TextEditingController _codeController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  Future<Response<EmailVerifyCodeOutput>?> _enter() async {
    setState(() {
      _isLoading = true;
    });

    final email = context.read<BlocAuthorization>().state.email;
    print('Email: $email');

    Response<EmailVerifyCodeOutput>? result;

    if (email != null && email.isNotEmpty) {
      result = await AuthRepository.emailVerifyCode(
        email: email,
        code: _codeController.text,
      );
    }

    setState(() {
      _isLoading = false;
    });

    return result;
  }

  @override
  Widget build(BuildContext context) {
    return Wrapper(
      appBar: CupertinoNavigationBar(
        automaticallyImplyLeading: true,
        previousPageTitle: '',
        middle: Text('Авторизация', style: Fonts.labelMedium),
      ),
      body: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Center(
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Подтвердите почту', style: Fonts.titleSmall),
                SizedBox(height: 32.0),
                TextFormField(
                  decoration: InputDecoration(
                    labelText: 'Код',
                    counterText: '',
                  ),
                  style: Fonts.labelLarge.merge(TextStyle(letterSpacing: 10.0)),
                  textAlign: TextAlign.center,
                  maxLength: 6,
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  controller: _codeController,
                  validator: (value) {
                    if (value == null || value.length != 6) {
                      return 'Введите 6 цифр';
                    }

                    return null;
                  },
                ),
                SizedBox(height: 32.0),
                CustomElevatedButton(
                  type: CustomElevatedButtonTypes.accent,
                  onPressed: () async {
                    if (_formKey.currentState!.validate()) {
                      final result = await _enter();
                      if (result?.statusCode == 200 && mounted) {
                        if (result?.data?.user == null) {
                          context.router.push(RegistrationRouteStep3());
                        }

                        final data = result!.data;
                        if (data == null) return;

                        final prefs = FlutterSecureStorage();
                        prefs.write(
                          key: 'accessToken',
                          value: data.accessToken,
                        );
                        prefs.write(
                          key: 'refreshToken',
                          value: data.refreshToken,
                        );
                        prefs.write(key: 'deviceId', value: data.deviceId);

                        context.read<BlocUser>().add(
                          SetSelf(data.user ?? User()),
                        );

                        context.router.push(MainRoute());
                      }
                    }
                  },
                  text: 'Готово',
                  child: _isLoading ? CupertinoActivityIndicator() : null,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
