import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:tony_clou_app/core/navigation/index.gr.dart';
import 'package:tony_clou_app/features/authorization/data/repository/index.dart';
import 'package:tony_clou_app/features/authorization/presentation/bloc/index.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/wrapper/index.dart';
import 'package:tony_clou_app/shared/widgets/interactive/elevated_button.dart';
import 'package:tony_clou_app/shared/widgets/interactive/ghost_button.dart';

@RoutePage()
class AuthorizationScreen extends StatefulWidget {
  const AuthorizationScreen({super.key});

  @override
  State<AuthorizationScreen> createState() => _AuthorizationScreenState();
}

class _AuthorizationScreenState extends State<AuthorizationScreen> {
  bool _isLoading = false;
  PackageInfo? _appInfo;
  final TextEditingController _emailController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  void _getVersion() async {
    final PackageInfo appInfo = await PackageInfo.fromPlatform();
    setState(() {
      _appInfo = appInfo;
    });
  }

  Future<bool> _enter() async {
    setState(() {
      _isLoading = true;
    });

    final result = await AuthRepository.emailVerify(
      email: _emailController.text,
    );
    final ok = result.statusCode == 202;

    setState(() {
      _isLoading = false;
    });

    return ok;
  }

  @override
  void initState() {
    super.initState();
    _getVersion();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Wrapper(
      body: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          children: [
            Form(
              key: _formKey,
              child: Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Hero(
                      tag: 'app_title',
                      transitionOnUserGestures: true,
                      child: Material(
                        type: MaterialType.transparency,
                        child: Text(
                          'Tony Clou App',
                          style: Fonts.headlineLarge,
                          textAlign: TextAlign.left,
                        ),
                      ),
                    ),
                    SizedBox(height: 32.0),
                    TextFormField(
                      controller: _emailController,
                      decoration: InputDecoration(labelText: 'Email'),
                      keyboardType: TextInputType.emailAddress,
                      style: Fonts.labelSmall,
                      onChanged: (value) {
                        context.read<BlocAuthorization>().add(
                          SetAuthEmail(value),
                        );
                      },
                      validator: (value) {
                        // Проверка на пустое значение
                        if (value == null || value.trim().isEmpty) {
                          return 'Пожалуйста, введите Email';
                        }

                        // Регулярное выражение для email
                        final bool emailValid = RegExp(
                          r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$',
                          caseSensitive: false,
                          multiLine: false,
                        ).hasMatch(value.trim());

                        if (!emailValid) {
                          return 'Пожалуйста, введите корректный Email';
                        }

                        return null;
                      },
                    ),
                    SizedBox(height: 32.0),
                    CustomElevatedButton(
                      type: CustomElevatedButtonTypes.accent,
                      onPressed: () async {
                        if (_formKey.currentState!.validate()) {
                          final ok = await _enter();
                          if (ok && mounted) {
                            context.router.push(VerifyCodeRoute());
                          }
                        }
                      },
                      text: 'Войти',
                      child: _isLoading ? CupertinoActivityIndicator() : null,
                    ),
                    SizedBox(height: 20.0),
                    GhostButton(
                      onTap: () => context.router.push(RegistationRoute()),
                      child: Text(
                        'Нет аккаунта? Создайте!',
                        style: Fonts.labelSmall.merge(
                          TextStyle(
                            color:
                                isDarkTheme
                                    ? AppColors.darkSecondary
                                    : AppColors.lightSecondary,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 20.0),
                    GhostButton(
                      onTap:
                          () => context.router.push(AlternativeSignInRoute()),
                      child: Text(
                        'Альтернативный вход',
                        style: Fonts.labelSmall.merge(
                          TextStyle(
                            color:
                                isDarkTheme
                                    ? AppColors.darkSecondary
                                    : AppColors.lightSecondary,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (_appInfo?.version != null)
              Text(
                _appInfo!.version,
                style: Fonts.bodySmall.merge(
                  TextStyle(
                    color:
                        isDarkTheme
                            ? AppColors.darkDescription
                            : AppColors.lightDescription,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
