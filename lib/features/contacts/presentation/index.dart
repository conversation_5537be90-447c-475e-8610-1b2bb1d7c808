import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/interactive/ghost_button.dart';
import 'package:url_launcher/url_launcher.dart';

@RoutePage()
class ContactsScreen extends StatelessWidget {
  const ContactsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CupertinoNavigationBar(
        backgroundColor: CupertinoColors.systemBackground.resolveFrom(context),
        border: Border(
          bottom: BorderSide(
            color: CupertinoColors.separator.resolveFrom(context),
            width: 0.5,
          ),
        ),
        previousPageTitle: '',
        middle: const Text(
          'Контакты',
          style: TextStyle(fontSize: 17.0, fontWeight: FontWeight.w600),
        ),
      ),
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Website section
            _buildContactSection(
              context,
              'Веб-сайт',
              CupertinoIcons.globe,
              CupertinoColors.systemBlue,
              [
                _ContactItem(
                  title: 'Официальный сайт',
                  subtitle: 'tonyclou.ru',
                  onTap: () => _launchUrl('https://tonyclou.ru'),
                  onLongPress:
                      () => _copyToClipboard(context, 'https://tonyclou.ru'),
                ),
              ],
            ),

            SizedBox(height: 20.0),

            // Phone contacts section
            _buildContactSection(
              context,
              'Телефоны',
              CupertinoIcons.phone,
              CupertinoColors.systemGreen,
              [
                _ContactItem(
                  title: 'Антон Алексеевич',
                  subtitle: '+7 (912) 286-63-92',
                  description: 'Продюссер',
                  onTap: () => _makePhoneCall('+79122866392'),
                  onLongPress: () => _copyToClipboard(context, '+79122866392'),
                ),
                _ContactItem(
                  title: 'Илья',
                  subtitle: '+7 (922) 169-98-25',
                  description: 'Работа с резидентами',
                  onTap: () => _makePhoneCall('+79992345678'),
                  onLongPress: () => _copyToClipboard(context, '+79221699825'),
                ),
                _ContactItem(
                  title: 'Елизавета',
                  subtitle: '+7 (919) 395-72-70',
                  description: 'По сотрудничеству',
                  onTap: () => _makePhoneCall('+79193957270'),
                  onLongPress: () => _copyToClipboard(context, '+79193957270'),
                ),
              ],
            ),

            SizedBox(height: 20.0),

            // Email section
            _buildContactSection(
              context,
              'Электронная почта',
              CupertinoIcons.mail,
              CupertinoColors.systemOrange,
              [
                _ContactItem(
                  title: 'Общие вопросы',
                  subtitle: '<EMAIL>',
                  onTap: () => _sendEmail('<EMAIL>'),
                  onLongPress:
                      () => _copyToClipboard(context, '<EMAIL>'),
                ),
              ],
            ),

            SizedBox(height: 20.0),

            // Social media section
            _buildContactSection(
              context,
              'Социальные сети',
              CupertinoIcons.share,
              CupertinoColors.systemPurple,
              [
                _ContactItem(
                  title: 'Telegram',
                  subtitle: '@tonyclouownstyle',
                  onTap: () => _launchUrl('https://t.me/tonyclouownstyle'),
                  onLongPress:
                      () => _copyToClipboard(context, '@tonyclouownstyle'),
                ),
                _ContactItem(
                  title: 'I****gram',
                  subtitle: '@tonyclou_ownstyle',
                  onTap:
                      () =>
                          _launchUrl('https://instagram.com/tonyclou_ownstyle'),
                  onLongPress:
                      () => _copyToClipboard(context, '@tonyclouownstyle'),
                ),
                _ContactItem(
                  title: 'VK',
                  subtitle: '@tcownstyle',
                  onTap: () => _launchUrl('https://vk.com/tcownstyle'),
                  onLongPress: () => _copyToClipboard(context, '@tcownstyle'),
                ),
              ],
            ),

            SizedBox(height: 20.0),

            // Help text
            Container(
              padding: EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: CupertinoColors.systemGrey6.resolveFrom(context),
                borderRadius: BorderRadius.circular(12.0),
              ),
              child: Column(
                children: [
                  Icon(
                    CupertinoIcons.info_circle,
                    color: CupertinoColors.secondaryLabel.resolveFrom(context),
                    size: 24.0,
                  ),
                  SizedBox(height: 8.0),
                  Text(
                    'Нажмите для звонка или отправки сообщения.\nДлительное нажатие для копирования.',
                    style: Fonts.bodySmall.copyWith(
                      color: CupertinoColors.secondaryLabel.resolveFrom(
                        context,
                      ),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactSection(
    BuildContext context,
    String title,
    IconData icon,
    Color iconColor,
    List<_ContactItem> items,
  ) {
    return Container(
      padding: EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: CupertinoColors.systemBackground.resolveFrom(context),
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(
          color: CupertinoColors.separator.resolveFrom(context),
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  color: iconColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Icon(icon, color: iconColor, size: 20.0),
              ),
              SizedBox(width: 12.0),
              Text(
                title,
                style: Fonts.labelLarge.copyWith(fontWeight: FontWeight.w600),
              ),
            ],
          ),
          SizedBox(height: 12.0),
          ...items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (index > 0) SizedBox(height: 12.0),
                // Divider(
                //   height: 12.0,
                //   color: CupertinoColors.separator.resolveFrom(context),
                // ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.title,
                      style: Fonts.bodyMedium.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    GhostButton(
                      onTap: item.onTap,
                      onLongPress: item.onLongPress,
                      color: CupertinoColors.systemBlue,
                      child: Text(
                        item.subtitle,
                        style: Fonts.bodyMedium.copyWith(
                          color: CupertinoColors.systemBlue,
                        ),
                      ),
                    ),
                    if (item.description != null)
                      Text(
                        item.description!,
                        style: Fonts.bodySmall.copyWith(
                          color: CupertinoColors.secondaryLabel.resolveFrom(
                            context,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            );
          }),
        ],
      ),
    );
  }

  Future<void> _launchUrl(String url) async {
    final uri = Uri.parse(url);
    await launchUrl(uri);
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final uri = Uri.parse('tel:$phoneNumber');
    await launchUrl(uri);
  }

  Future<void> _sendEmail(String email) async {
    final uri = Uri.parse('mailto:$email');
    await launchUrl(uri);
  }

  void _copyToClipboard(BuildContext context, String text) {
    Clipboard.setData(ClipboardData(text: text));
    // Show feedback
    showCupertinoDialog(
      context: context,
      builder:
          (context) => CupertinoAlertDialog(
            title: Text('Скопировано'),
            content: Text('$text скопирован в буфер обмена'),
            actions: [
              CupertinoDialogAction(
                child: Text('OK'),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
    );
  }
}

class _ContactItem {
  final String title;
  final String subtitle;
  final String? description;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const _ContactItem({
    required this.title,
    required this.subtitle,
    this.description,
    this.onTap,
    this.onLongPress,
  });
}
