import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:tony_clou_app/features/main/presentation/bloc/user/index.dart';
import 'package:tony_clou_app/features/qr/data/repository/index.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/wrapper/index.dart';
import 'package:tony_clou_app/shared/widgets/interactive/elevated_button.dart';

@RoutePage()
class ScanScreen extends StatefulWidget {
  const ScanScreen({super.key});

  @override
  State<ScanScreen> createState() => _ScanScreenState();
}

class _ScanScreenState extends State<ScanScreen> {
  final MobileScannerController controller = MobileScannerController(
    autoZoom: true,
    formats: [BarcodeFormat.qrCode],
    detectionTimeoutMs: 500,
  );
  bool _isScanning = true;
  bool _isFlashOn = false;
  String? _lastScannedValue;
  bool _isProcessing = false;
  final ImagePicker _picker = ImagePicker();

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  void _handleScan(BarcodeCapture capture) async {
    if (!_isScanning || _isProcessing) return;

    setState(() {
      _isProcessing = true;
    });

    final barcode = capture.barcodes.first;
    final scannedValue = barcode.rawValue;

    if (scannedValue != null && scannedValue != _lastScannedValue) {
      setState(() {
        _lastScannedValue = scannedValue;
        _isScanning = false;
      });

      final result = await QrRepostory.validateQr(scannedValue);
      final data = result.data;

      if (data?.qr != null && data?.user != null && mounted) {
        context.read<BlocUser>().add(SetSelf(data!.user!));
        context.maybePop();
      }

      // Resume scanning after a delay
      await Future.delayed(const Duration(seconds: 2));
      setState(() {
        _isScanning = true;
        _lastScannedValue = null;
        _isProcessing = false;
      });
    } else {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  Future<void> _openGallery() async {
    if (_isProcessing) return;

    try {
      final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
      if (image == null || !mounted) {
        setState(() {
          _isProcessing = false;
        });
        return;
      }

      // Analyze the image for QR codes
      final result = await controller.analyzeImage(image.path);
      if (result != null && result.barcodes.isNotEmpty) {
        _handleScan(result);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('QR-код не найден в изображении')),
          );
        }
        setState(() {
          _isProcessing = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Ошибка: $e')));
      }
      setState(() {
        _isProcessing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Wrapper(
      appBar: CupertinoNavigationBar(
        automaticallyImplyLeading: true,
        previousPageTitle: 'Баланс',
        middle: Text('Сканирование QR', style: Fonts.labelMedium),
        trailing: IconButton(
          icon: Icon(
            _isFlashOn
                ? CupertinoIcons.lightbulb
                : CupertinoIcons.lightbulb_slash,
          ),
          onPressed: () async {
            await controller.toggleTorch();
            setState(() {
              _isFlashOn = controller.torchEnabled;
            });
          },
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AspectRatio(
                aspectRatio: 1,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16.0),
                    border: Border.all(
                      color:
                          isDarkTheme
                              ? AppColors.darkStroke
                              : AppColors.lightStroke,
                      width: 2.0,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16.0),
                    child: MobileScanner(
                      controller: controller,
                      onDetect: _handleScan,
                      errorBuilder: (context, error) {
                        return Center(
                          child: Text(
                            'Ошибка камеры: ${error.errorDetails?.message}',
                            style: Fonts.bodyMedium.copyWith(
                              color:
                                  isDarkTheme
                                      ? AppColors.darkError
                                      : AppColors.lightError,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
              if (_isProcessing) SizedBox(height: 12.0),
              if (_isProcessing) CupertinoActivityIndicator(),
              const SizedBox(height: 16.0),
              Text(
                _isScanning
                    ? 'Наведите камеру на QR-код'
                    : 'Обработка QR-кода...',
                style: Fonts.bodyMedium,
              ),
              const SizedBox(height: 16.0),
              CustomElevatedButton(
                onPressed: _openGallery,
                text: 'Открыть из галереи',
              ),
            ],
          ),
        ),
      ),
    );
  }
}
