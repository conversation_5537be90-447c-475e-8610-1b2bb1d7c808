import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:tony_clou_app/features/qr/data/models/index.dart';
import 'package:tony_clou_app/features/qr/data/models/qr.dart';
import 'package:tony_clou_app/shared/data/api.dart';

class QrRepostory {
  static Future<Response<CreateQrOutput>> createQr(QrCodeModel qrCode) async {
    final body = qrCode.toJson();

    final request = await API.request<CreateQrOutput>(
      url: '/qr/createQr',
      body: body,
      fromJson: CreateQrOutput.fromJson,
      method: 'POST',
    );

    return request;
  }

  static Future<Response<ValidateQrOutput>> validateQr(String id) async {
    final body = {'id': id};

    final request = await API.request<ValidateQrOutput>(
      url: '/qr/validateQr',
      body: jsonEncode(body),
      fromJson: ValidateQrOutput.fromJson,
      method: 'POST',
    );

    return request;
  }

  static Future<Response<DeleteQrsOutput>> deleteQrs(String id) async {
    final request = await API.request<DeleteQrsOutput>(
      url: '/qr/deleteQrs',
      fromJson: DeleteQrsOutput.fromJson,
      method: 'POST',
    );

    return request;
  }
}
