// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'qr.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_QrCodeModel _$QrCodeModelFromJson(Map<String, dynamic> json) => _QrCodeModel(
  id: json['id'] as String?,
  moneyCoin: (json['moneyCoin'] as num?)?.toInt(),
  moneyGem:
      json['moneyGem'] == null
          ? null
          : UserMoneyGem.fromJson(json['moneyGem'] as Map<String, dynamic>),
  description: json['description'] as String?,
  expiresAt:
      json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
);

Map<String, dynamic> _$QrCodeModelToJson(_QrCodeModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.moneyCoin case final value?) 'moneyCoin': value,
      if (instance.moneyGem case final value?) 'moneyGem': value,
      if (instance.description case final value?) 'description': value,
      if (instance.expiresAt?.toIso8601String() case final value?)
        'expiresAt': value,
    };
