import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';

part 'qr.freezed.dart';
part 'qr.g.dart';

@freezed
abstract class QrCodeModel with _$QrCodeModel {
  @JsonSerializable(includeIfNull: false)
  factory QrCodeModel({
    String? id,
    int? moneyCoin,
    UserMoneyGem? moneyGem,
    String? description,
    DateTime? expiresAt,
  }) = _QrCodeModel;

  factory QrCodeModel.fromJson(Map<String, dynamic> json) =>
      _$QrCodeModelFromJson(json);
}
