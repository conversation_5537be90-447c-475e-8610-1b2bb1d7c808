// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'index.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CreateQrOutput {

 String? get id; QrCodeModel? get qrCode;
/// Create a copy of CreateQrOutput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CreateQrOutputCopyWith<CreateQrOutput> get copyWith => _$CreateQrOutputCopyWithImpl<CreateQrOutput>(this as CreateQrOutput, _$identity);

  /// Serializes this CreateQrOutput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreateQrOutput&&(identical(other.id, id) || other.id == id)&&(identical(other.qrCode, qrCode) || other.qrCode == qrCode));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,qrCode);

@override
String toString() {
  return 'CreateQrOutput(id: $id, qrCode: $qrCode)';
}


}

/// @nodoc
abstract mixin class $CreateQrOutputCopyWith<$Res>  {
  factory $CreateQrOutputCopyWith(CreateQrOutput value, $Res Function(CreateQrOutput) _then) = _$CreateQrOutputCopyWithImpl;
@useResult
$Res call({
 String? id, QrCodeModel? qrCode
});


$QrCodeModelCopyWith<$Res>? get qrCode;

}
/// @nodoc
class _$CreateQrOutputCopyWithImpl<$Res>
    implements $CreateQrOutputCopyWith<$Res> {
  _$CreateQrOutputCopyWithImpl(this._self, this._then);

  final CreateQrOutput _self;
  final $Res Function(CreateQrOutput) _then;

/// Create a copy of CreateQrOutput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? qrCode = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,qrCode: freezed == qrCode ? _self.qrCode : qrCode // ignore: cast_nullable_to_non_nullable
as QrCodeModel?,
  ));
}
/// Create a copy of CreateQrOutput
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$QrCodeModelCopyWith<$Res>? get qrCode {
    if (_self.qrCode == null) {
    return null;
  }

  return $QrCodeModelCopyWith<$Res>(_self.qrCode!, (value) {
    return _then(_self.copyWith(qrCode: value));
  });
}
}


/// @nodoc

@JsonSerializable(includeIfNull: false)
class _CreateQrOutput implements CreateQrOutput {
   _CreateQrOutput({this.id, this.qrCode});
  factory _CreateQrOutput.fromJson(Map<String, dynamic> json) => _$CreateQrOutputFromJson(json);

@override final  String? id;
@override final  QrCodeModel? qrCode;

/// Create a copy of CreateQrOutput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CreateQrOutputCopyWith<_CreateQrOutput> get copyWith => __$CreateQrOutputCopyWithImpl<_CreateQrOutput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CreateQrOutputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CreateQrOutput&&(identical(other.id, id) || other.id == id)&&(identical(other.qrCode, qrCode) || other.qrCode == qrCode));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,qrCode);

@override
String toString() {
  return 'CreateQrOutput(id: $id, qrCode: $qrCode)';
}


}

/// @nodoc
abstract mixin class _$CreateQrOutputCopyWith<$Res> implements $CreateQrOutputCopyWith<$Res> {
  factory _$CreateQrOutputCopyWith(_CreateQrOutput value, $Res Function(_CreateQrOutput) _then) = __$CreateQrOutputCopyWithImpl;
@override @useResult
$Res call({
 String? id, QrCodeModel? qrCode
});


@override $QrCodeModelCopyWith<$Res>? get qrCode;

}
/// @nodoc
class __$CreateQrOutputCopyWithImpl<$Res>
    implements _$CreateQrOutputCopyWith<$Res> {
  __$CreateQrOutputCopyWithImpl(this._self, this._then);

  final _CreateQrOutput _self;
  final $Res Function(_CreateQrOutput) _then;

/// Create a copy of CreateQrOutput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? qrCode = freezed,}) {
  return _then(_CreateQrOutput(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,qrCode: freezed == qrCode ? _self.qrCode : qrCode // ignore: cast_nullable_to_non_nullable
as QrCodeModel?,
  ));
}

/// Create a copy of CreateQrOutput
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$QrCodeModelCopyWith<$Res>? get qrCode {
    if (_self.qrCode == null) {
    return null;
  }

  return $QrCodeModelCopyWith<$Res>(_self.qrCode!, (value) {
    return _then(_self.copyWith(qrCode: value));
  });
}
}


/// @nodoc
mixin _$ValidateQrOutput {

 QrCodeModel? get qr; User? get user;
/// Create a copy of ValidateQrOutput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ValidateQrOutputCopyWith<ValidateQrOutput> get copyWith => _$ValidateQrOutputCopyWithImpl<ValidateQrOutput>(this as ValidateQrOutput, _$identity);

  /// Serializes this ValidateQrOutput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ValidateQrOutput&&(identical(other.qr, qr) || other.qr == qr)&&(identical(other.user, user) || other.user == user));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,qr,user);

@override
String toString() {
  return 'ValidateQrOutput(qr: $qr, user: $user)';
}


}

/// @nodoc
abstract mixin class $ValidateQrOutputCopyWith<$Res>  {
  factory $ValidateQrOutputCopyWith(ValidateQrOutput value, $Res Function(ValidateQrOutput) _then) = _$ValidateQrOutputCopyWithImpl;
@useResult
$Res call({
 QrCodeModel? qr, User? user
});


$QrCodeModelCopyWith<$Res>? get qr;$UserCopyWith<$Res>? get user;

}
/// @nodoc
class _$ValidateQrOutputCopyWithImpl<$Res>
    implements $ValidateQrOutputCopyWith<$Res> {
  _$ValidateQrOutputCopyWithImpl(this._self, this._then);

  final ValidateQrOutput _self;
  final $Res Function(ValidateQrOutput) _then;

/// Create a copy of ValidateQrOutput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? qr = freezed,Object? user = freezed,}) {
  return _then(_self.copyWith(
qr: freezed == qr ? _self.qr : qr // ignore: cast_nullable_to_non_nullable
as QrCodeModel?,user: freezed == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as User?,
  ));
}
/// Create a copy of ValidateQrOutput
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$QrCodeModelCopyWith<$Res>? get qr {
    if (_self.qr == null) {
    return null;
  }

  return $QrCodeModelCopyWith<$Res>(_self.qr!, (value) {
    return _then(_self.copyWith(qr: value));
  });
}/// Create a copy of ValidateQrOutput
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserCopyWith<$Res>? get user {
    if (_self.user == null) {
    return null;
  }

  return $UserCopyWith<$Res>(_self.user!, (value) {
    return _then(_self.copyWith(user: value));
  });
}
}


/// @nodoc

@JsonSerializable(includeIfNull: false)
class _ValidateQrOutput implements ValidateQrOutput {
   _ValidateQrOutput({this.qr, this.user});
  factory _ValidateQrOutput.fromJson(Map<String, dynamic> json) => _$ValidateQrOutputFromJson(json);

@override final  QrCodeModel? qr;
@override final  User? user;

/// Create a copy of ValidateQrOutput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ValidateQrOutputCopyWith<_ValidateQrOutput> get copyWith => __$ValidateQrOutputCopyWithImpl<_ValidateQrOutput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ValidateQrOutputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ValidateQrOutput&&(identical(other.qr, qr) || other.qr == qr)&&(identical(other.user, user) || other.user == user));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,qr,user);

@override
String toString() {
  return 'ValidateQrOutput(qr: $qr, user: $user)';
}


}

/// @nodoc
abstract mixin class _$ValidateQrOutputCopyWith<$Res> implements $ValidateQrOutputCopyWith<$Res> {
  factory _$ValidateQrOutputCopyWith(_ValidateQrOutput value, $Res Function(_ValidateQrOutput) _then) = __$ValidateQrOutputCopyWithImpl;
@override @useResult
$Res call({
 QrCodeModel? qr, User? user
});


@override $QrCodeModelCopyWith<$Res>? get qr;@override $UserCopyWith<$Res>? get user;

}
/// @nodoc
class __$ValidateQrOutputCopyWithImpl<$Res>
    implements _$ValidateQrOutputCopyWith<$Res> {
  __$ValidateQrOutputCopyWithImpl(this._self, this._then);

  final _ValidateQrOutput _self;
  final $Res Function(_ValidateQrOutput) _then;

/// Create a copy of ValidateQrOutput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? qr = freezed,Object? user = freezed,}) {
  return _then(_ValidateQrOutput(
qr: freezed == qr ? _self.qr : qr // ignore: cast_nullable_to_non_nullable
as QrCodeModel?,user: freezed == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as User?,
  ));
}

/// Create a copy of ValidateQrOutput
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$QrCodeModelCopyWith<$Res>? get qr {
    if (_self.qr == null) {
    return null;
  }

  return $QrCodeModelCopyWith<$Res>(_self.qr!, (value) {
    return _then(_self.copyWith(qr: value));
  });
}/// Create a copy of ValidateQrOutput
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserCopyWith<$Res>? get user {
    if (_self.user == null) {
    return null;
  }

  return $UserCopyWith<$Res>(_self.user!, (value) {
    return _then(_self.copyWith(user: value));
  });
}
}


/// @nodoc
mixin _$DeleteQrsOutput {

 int? get length;
/// Create a copy of DeleteQrsOutput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DeleteQrsOutputCopyWith<DeleteQrsOutput> get copyWith => _$DeleteQrsOutputCopyWithImpl<DeleteQrsOutput>(this as DeleteQrsOutput, _$identity);

  /// Serializes this DeleteQrsOutput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DeleteQrsOutput&&(identical(other.length, length) || other.length == length));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,length);

@override
String toString() {
  return 'DeleteQrsOutput(length: $length)';
}


}

/// @nodoc
abstract mixin class $DeleteQrsOutputCopyWith<$Res>  {
  factory $DeleteQrsOutputCopyWith(DeleteQrsOutput value, $Res Function(DeleteQrsOutput) _then) = _$DeleteQrsOutputCopyWithImpl;
@useResult
$Res call({
 int? length
});




}
/// @nodoc
class _$DeleteQrsOutputCopyWithImpl<$Res>
    implements $DeleteQrsOutputCopyWith<$Res> {
  _$DeleteQrsOutputCopyWithImpl(this._self, this._then);

  final DeleteQrsOutput _self;
  final $Res Function(DeleteQrsOutput) _then;

/// Create a copy of DeleteQrsOutput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? length = freezed,}) {
  return _then(_self.copyWith(
length: freezed == length ? _self.length : length // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc

@JsonSerializable(includeIfNull: false)
class _DeleteQrsOutput implements DeleteQrsOutput {
   _DeleteQrsOutput({this.length});
  factory _DeleteQrsOutput.fromJson(Map<String, dynamic> json) => _$DeleteQrsOutputFromJson(json);

@override final  int? length;

/// Create a copy of DeleteQrsOutput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DeleteQrsOutputCopyWith<_DeleteQrsOutput> get copyWith => __$DeleteQrsOutputCopyWithImpl<_DeleteQrsOutput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DeleteQrsOutputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DeleteQrsOutput&&(identical(other.length, length) || other.length == length));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,length);

@override
String toString() {
  return 'DeleteQrsOutput(length: $length)';
}


}

/// @nodoc
abstract mixin class _$DeleteQrsOutputCopyWith<$Res> implements $DeleteQrsOutputCopyWith<$Res> {
  factory _$DeleteQrsOutputCopyWith(_DeleteQrsOutput value, $Res Function(_DeleteQrsOutput) _then) = __$DeleteQrsOutputCopyWithImpl;
@override @useResult
$Res call({
 int? length
});




}
/// @nodoc
class __$DeleteQrsOutputCopyWithImpl<$Res>
    implements _$DeleteQrsOutputCopyWith<$Res> {
  __$DeleteQrsOutputCopyWithImpl(this._self, this._then);

  final _DeleteQrsOutput _self;
  final $Res Function(_DeleteQrsOutput) _then;

/// Create a copy of DeleteQrsOutput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? length = freezed,}) {
  return _then(_DeleteQrsOutput(
length: freezed == length ? _self.length : length // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on
