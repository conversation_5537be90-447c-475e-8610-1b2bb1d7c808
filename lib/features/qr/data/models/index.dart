import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:tony_clou_app/features/qr/data/models/qr.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';

part 'index.freezed.dart';
part 'index.g.dart';

@freezed
abstract class CreateQrOutput with _$CreateQrOutput {
  @JsonSerializable(includeIfNull: false)
  factory CreateQrOutput({String? id, QrCodeModel? qrCode}) = _CreateQrOutput;

  factory CreateQrOutput.fromJson(Map<String, dynamic> json) =>
      _$CreateQrOutputFromJson(json);
}

@freezed
abstract class ValidateQrOutput with _$ValidateQrOutput {
  @JsonSerializable(includeIfNull: false)
  factory ValidateQrOutput({QrCodeModel? qr, User? user}) = _ValidateQrOutput;

  factory ValidateQrOutput.fromJson(Map<String, dynamic> json) =>
      _$ValidateQrOutputFromJson(json);
}

@freezed
abstract class DeleteQrsOutput with _$DeleteQrsOutput {
  @JsonSerializable(includeIfNull: false)
  factory DeleteQrsOutput({int? length}) = _DeleteQrsOutput;

  factory DeleteQrsOutput.fromJson(Map<String, dynamic> json) =>
      _$DeleteQrsOutputFromJson(json);
}
