// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'index.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CreateQrOutput _$CreateQrOutputFromJson(Map<String, dynamic> json) =>
    _CreateQrOutput(
      id: json['id'] as String?,
      qrCode:
          json['qrCode'] == null
              ? null
              : QrCodeModel.fromJson(json['qrCode'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CreateQrOutputToJson(_CreateQrOutput instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.qrCode case final value?) 'qrCode': value,
    };

_ValidateQrOutput _$ValidateQrOutputFromJson(Map<String, dynamic> json) =>
    _ValidateQrOutput(
      qr:
          json['qr'] == null
              ? null
              : QrCodeModel.fromJson(json['qr'] as Map<String, dynamic>),
      user:
          json['user'] == null
              ? null
              : User.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ValidateQrOutputToJson(_ValidateQrOutput instance) =>
    <String, dynamic>{
      if (instance.qr case final value?) 'qr': value,
      if (instance.user case final value?) 'user': value,
    };

_DeleteQrsOutput _$DeleteQrsOutputFromJson(Map<String, dynamic> json) =>
    _DeleteQrsOutput(length: (json['length'] as num?)?.toInt());

Map<String, dynamic> _$DeleteQrsOutputToJson(_DeleteQrsOutput instance) =>
    <String, dynamic>{if (instance.length case final value?) 'length': value};
