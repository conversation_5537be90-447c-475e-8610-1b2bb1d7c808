// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'qr.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$QrCodeModel {

 String? get id; int? get moneyCoin; UserMoneyGem? get moneyGem; String? get description; DateTime? get expiresAt;
/// Create a copy of QrCodeModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$QrCodeModelCopyWith<QrCodeModel> get copyWith => _$QrCodeModelCopyWithImpl<QrCodeModel>(this as QrCodeModel, _$identity);

  /// Serializes this QrCodeModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is QrCodeModel&&(identical(other.id, id) || other.id == id)&&(identical(other.moneyCoin, moneyCoin) || other.moneyCoin == moneyCoin)&&(identical(other.moneyGem, moneyGem) || other.moneyGem == moneyGem)&&(identical(other.description, description) || other.description == description)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,moneyCoin,moneyGem,description,expiresAt);

@override
String toString() {
  return 'QrCodeModel(id: $id, moneyCoin: $moneyCoin, moneyGem: $moneyGem, description: $description, expiresAt: $expiresAt)';
}


}

/// @nodoc
abstract mixin class $QrCodeModelCopyWith<$Res>  {
  factory $QrCodeModelCopyWith(QrCodeModel value, $Res Function(QrCodeModel) _then) = _$QrCodeModelCopyWithImpl;
@useResult
$Res call({
 String? id, int? moneyCoin, UserMoneyGem? moneyGem, String? description, DateTime? expiresAt
});


$UserMoneyGemCopyWith<$Res>? get moneyGem;

}
/// @nodoc
class _$QrCodeModelCopyWithImpl<$Res>
    implements $QrCodeModelCopyWith<$Res> {
  _$QrCodeModelCopyWithImpl(this._self, this._then);

  final QrCodeModel _self;
  final $Res Function(QrCodeModel) _then;

/// Create a copy of QrCodeModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? moneyCoin = freezed,Object? moneyGem = freezed,Object? description = freezed,Object? expiresAt = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,moneyCoin: freezed == moneyCoin ? _self.moneyCoin : moneyCoin // ignore: cast_nullable_to_non_nullable
as int?,moneyGem: freezed == moneyGem ? _self.moneyGem : moneyGem // ignore: cast_nullable_to_non_nullable
as UserMoneyGem?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,expiresAt: freezed == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}
/// Create a copy of QrCodeModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserMoneyGemCopyWith<$Res>? get moneyGem {
    if (_self.moneyGem == null) {
    return null;
  }

  return $UserMoneyGemCopyWith<$Res>(_self.moneyGem!, (value) {
    return _then(_self.copyWith(moneyGem: value));
  });
}
}


/// @nodoc

@JsonSerializable(includeIfNull: false)
class _QrCodeModel implements QrCodeModel {
   _QrCodeModel({this.id, this.moneyCoin, this.moneyGem, this.description, this.expiresAt});
  factory _QrCodeModel.fromJson(Map<String, dynamic> json) => _$QrCodeModelFromJson(json);

@override final  String? id;
@override final  int? moneyCoin;
@override final  UserMoneyGem? moneyGem;
@override final  String? description;
@override final  DateTime? expiresAt;

/// Create a copy of QrCodeModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$QrCodeModelCopyWith<_QrCodeModel> get copyWith => __$QrCodeModelCopyWithImpl<_QrCodeModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$QrCodeModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _QrCodeModel&&(identical(other.id, id) || other.id == id)&&(identical(other.moneyCoin, moneyCoin) || other.moneyCoin == moneyCoin)&&(identical(other.moneyGem, moneyGem) || other.moneyGem == moneyGem)&&(identical(other.description, description) || other.description == description)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,moneyCoin,moneyGem,description,expiresAt);

@override
String toString() {
  return 'QrCodeModel(id: $id, moneyCoin: $moneyCoin, moneyGem: $moneyGem, description: $description, expiresAt: $expiresAt)';
}


}

/// @nodoc
abstract mixin class _$QrCodeModelCopyWith<$Res> implements $QrCodeModelCopyWith<$Res> {
  factory _$QrCodeModelCopyWith(_QrCodeModel value, $Res Function(_QrCodeModel) _then) = __$QrCodeModelCopyWithImpl;
@override @useResult
$Res call({
 String? id, int? moneyCoin, UserMoneyGem? moneyGem, String? description, DateTime? expiresAt
});


@override $UserMoneyGemCopyWith<$Res>? get moneyGem;

}
/// @nodoc
class __$QrCodeModelCopyWithImpl<$Res>
    implements _$QrCodeModelCopyWith<$Res> {
  __$QrCodeModelCopyWithImpl(this._self, this._then);

  final _QrCodeModel _self;
  final $Res Function(_QrCodeModel) _then;

/// Create a copy of QrCodeModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? moneyCoin = freezed,Object? moneyGem = freezed,Object? description = freezed,Object? expiresAt = freezed,}) {
  return _then(_QrCodeModel(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,moneyCoin: freezed == moneyCoin ? _self.moneyCoin : moneyCoin // ignore: cast_nullable_to_non_nullable
as int?,moneyGem: freezed == moneyGem ? _self.moneyGem : moneyGem // ignore: cast_nullable_to_non_nullable
as UserMoneyGem?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,expiresAt: freezed == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

/// Create a copy of QrCodeModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserMoneyGemCopyWith<$Res>? get moneyGem {
    if (_self.moneyGem == null) {
    return null;
  }

  return $UserMoneyGemCopyWith<$Res>(_self.moneyGem!, (value) {
    return _then(_self.copyWith(moneyGem: value));
  });
}
}

// dart format on
