import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/interactive/ghost_button.dart';
import 'package:url_launcher/url_launcher.dart';

// Константы и данные для LocationsScreen
class _LocationsScreenConstants {
  static const double padding = 16.0;
  static const double cardSpacing = 16.0;
  static const double borderRadius = 12.0;
  static const String screenTitle = 'Где нас найти';

  static const List<Map<String, String>> locations = [
    {
      'name': 'Центральный офис',
      'address': 'г. Екатеринбург,ул. Мамина-Сибиряка, 52',
      'city': 'Екатеринбург',
      'workingHours': 'Каждый день: 11:00 - 20:00',
      'phone': '+7 (919) 395-72-70 (Елизавета)',
    },
    {
      'name': 'Галерея "ТЦ Brand Stories"',
      'address':
          'Ул. Нескучная 3, (ТЦ Brand Stories Outlet в пространстве Galleria Russian Brands)',
      'city': 'Екатеринбург',
      'workingHours': 'Каждый день: 11:00 - 20:00',
      // 'phone': '+7 (495) 987-65-43',
    },
    {
      'name': 'Галерея "ТЦ VeerMall"',
      'address':
          'Ул. Космонавтов 108, (ТЦ VeerMall в пространстве Galleria Russian Brands)',
      'city': 'Екатеринбург',
      'workingHours': 'Каждый день: 11:00 - 20:00',
      // 'phone': '+7 (812) 555-12-34',
    },
  ];
}

@RoutePage()
class LocationsScreen extends StatelessWidget {
  const LocationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CupertinoNavigationBar(
        backgroundColor: CupertinoColors.systemBackground.resolveFrom(context),
        border: Border(
          bottom: BorderSide(color: CupertinoColors.separator, width: 0.5),
        ),
        previousPageTitle: '',
        middle: const Text(
          _LocationsScreenConstants.screenTitle,
          style: TextStyle(fontSize: 17.0, fontWeight: FontWeight.w600),
        ),
      ),
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        padding: const EdgeInsets.all(_LocationsScreenConstants.padding),
        child: Column(
          children:
              _LocationsScreenConstants.locations.asMap().entries.map((entry) {
                final index = entry.key;
                final location = entry.value;
                return Padding(
                  padding: EdgeInsets.only(
                    bottom:
                        index < _LocationsScreenConstants.locations.length - 1
                            ? _LocationsScreenConstants.cardSpacing
                            : 0,
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      color: CupertinoColors.systemBackground.resolveFrom(
                        context,
                      ),
                      borderRadius: BorderRadius.circular(
                        _LocationsScreenConstants.borderRadius,
                      ),
                      border: Border.all(
                        color: CupertinoColors.separator.resolveFrom(context),
                        width: 0.5,
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(
                        _LocationsScreenConstants.padding,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8.0),
                                decoration: BoxDecoration(
                                  color: CupertinoColors.systemBlue.withValues(
                                    alpha: 0.1,
                                  ),
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                                child: const Icon(
                                  CupertinoIcons.location_solid,
                                  color: CupertinoColors.systemBlue,
                                  size: 20.0,
                                ),
                              ),
                              const SizedBox(width: 12.0),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      location['name']!,
                                      style: Fonts.labelLarge.copyWith(
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    Text(
                                      location['city']!,
                                      style: Fonts.bodySmall.copyWith(
                                        color: CupertinoColors.secondaryLabel
                                            .resolveFrom(context),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12.0),
                          _buildInfoRow(
                            context,
                            CupertinoIcons.location,
                            location['address']!,
                          ),
                          SizedBox(height: 8.0),
                          GhostButton(
                            onTap: () {
                              // launch url
                              launchUrl(
                                Uri.parse(
                                  'https://yandex.ru/maps/?text=${location['address']}',
                                ),
                              );
                            },
                            child: Text(
                              'Смотреть на карте',
                              style: TextStyle(color: AppColors.lightSecondary),
                            ),
                          ),
                          const SizedBox(height: 8.0),
                          _buildInfoRow(
                            context,
                            CupertinoIcons.clock,
                            location['workingHours']!,
                          ),
                          if (location['phone'] != null)
                            const SizedBox(height: 8.0),
                          if (location['phone'] != null)
                            _buildInfoRow(
                              context,
                              CupertinoIcons.phone,
                              location['phone']!,
                            ),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
        ),
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, IconData icon, String text) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16.0,
          color: CupertinoColors.secondaryLabel.resolveFrom(context),
        ),
        SizedBox(width: 8.0),
        Expanded(
          child: Text(
            text,
            style: Fonts.bodyMedium.copyWith(
              color: CupertinoColors.label.resolveFrom(context),
            ),
          ),
        ),
      ],
    );
  }
}
