import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tony_clou_app/features/check/data/models/check.dart';
import 'package:tony_clou_app/features/check/data/models/index.dart';
import 'package:tony_clou_app/features/check/data/repository/index.dart';
import 'package:tony_clou_app/features/check/presentation/card.dart';
import 'package:tony_clou_app/shared/data/models/pagination.dart';
import 'package:tony_clou_app/shared/helpers/debouncer.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/wrapper/index.dart';

@RoutePage()
class ChecksListScreen extends StatefulWidget {
  const ChecksListScreen({super.key});

  @override
  State<ChecksListScreen> createState() => _ChecksListScreenState();
}

class _ChecksListScreenState extends State<ChecksListScreen> {
  List<Check> _checks = [];
  bool _isLoading = false;
  bool _isInitialLoading = true;
  int _currentPage = 1;
  final int _perPage = 10;
  bool _hasMore = true;
  String _currentQuery = '';

  final _debouncer = Debouncer();
  final _scrollController = ScrollController();
  final _searchController = TextEditingController();

  Future<void> _loadChecks({String? search, bool isRefresh = false}) async {
    if (_isLoading || (!_hasMore && !isRefresh)) return;

    // Update current query if search is provided
    if (search != null) {
      _currentQuery = search;
    }

    if (isRefresh) {
      setState(() {
        _currentPage = 1;
        _hasMore = true;
        _checks = [];
        _isInitialLoading = _checks.isEmpty;
      });
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await CheckRepository.getChecks(
        GetChecksInput(
          query: _currentQuery.isEmpty ? null : _currentQuery,
          pagination: Pagination(page: _currentPage, perPage: _perPage),
        ),
      );

      setState(() {
        if (result.data != null) {
          _checks =
              isRefresh
                  ? result.data!.items ?? []
                  : [..._checks, ...result.data!.items ?? []];
          _hasMore = (result.data!.items?.length ?? 0) == _perPage;
          _currentPage++;
        } else {
          _hasMore = false;
        }
        _isLoading = false;
        _isInitialLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _isInitialLoading = false;
        _hasMore = false;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _loadChecks();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent * 0.8) {
        _loadChecks();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Wrapper(
      appBar: CupertinoNavigationBar(
        automaticallyImplyLeading: true,
        previousPageTitle: 'Админка',
        middle: Text('Чеки (${_checks.length})', style: Fonts.labelMedium),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Поиск чеков...',
                prefixIcon: Icon(CupertinoIcons.search),
                suffixIcon:
                    _searchController.text.isNotEmpty
                        ? IconButton(
                          icon: Icon(CupertinoIcons.clear),
                          onPressed: () {
                            _searchController.clear();
                            _loadChecks(search: '', isRefresh: true);
                          },
                        )
                        : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.0),
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surface,
              ),
              onChanged: (value) {
                setState(() {}); // Для обновления suffixIcon
                _debouncer.run(
                  () => _loadChecks(search: value, isRefresh: true),
                );
              },
            ),
          ),
          Expanded(
            child:
                _isInitialLoading
                    ? Center(child: CircularProgressIndicator())
                    : RefreshIndicator.adaptive(
                      onRefresh: () => _loadChecks(isRefresh: true),
                      child:
                          _checks.isEmpty && !_isLoading
                              ? ListView(
                                children: [
                                  SizedBox(height: 100),
                                  Center(
                                    child: Column(
                                      children: [
                                        Icon(
                                          CupertinoIcons.square_list,
                                          size: 64,
                                          color: Theme.of(context)
                                              .colorScheme
                                              .onSurface
                                              .withValues(alpha: 0.5),
                                        ),
                                        SizedBox(height: 16),
                                        Text(
                                          _currentQuery.isEmpty
                                              ? 'Нет чеков'
                                              : 'Чеки не найдены',
                                          style: Theme.of(
                                            context,
                                          ).textTheme.titleMedium?.copyWith(
                                            color: Theme.of(context)
                                                .colorScheme
                                                .onSurface
                                                .withValues(alpha: 0.7),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              )
                              : ListView.separated(
                                controller: _scrollController,
                                padding: EdgeInsets.symmetric(horizontal: 12.0),
                                itemCount:
                                    _checks.length +
                                    (_isLoading && !_isInitialLoading ? 1 : 0),
                                itemBuilder: (context, index) {
                                  if (index == _checks.length && _isLoading) {
                                    return Padding(
                                      padding: EdgeInsets.all(16.0),
                                      child: Center(
                                        child: CircularProgressIndicator(),
                                      ),
                                    );
                                  }

                                  final check = _checks[index];
                                  return CheckCard(data: check);
                                },
                                separatorBuilder:
                                    (context, index) => SizedBox(height: 8.0),
                              ),
                    ),
          ),
        ],
      ),
    );
  }
}
