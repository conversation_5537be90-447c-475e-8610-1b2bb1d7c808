import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:tony_clou_app/features/admin/presentation/widgets/actions.dart';
import 'package:tony_clou_app/features/admin/presentation/widgets/qr_generation/currency.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/wrapper/index.dart';

@RoutePage()
class AdminScreen extends StatelessWidget {
  const AdminScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Wrapper(
      appBar: CupertinoNavigationBar(
        automaticallyImplyLeading: true,
        previousPageTitle: '',
        middle: Text('Админка', style: Fonts.labelMedium),
      ),
      body: ListView(
        physics: ClampingScrollPhysics(),
        padding: EdgeInsets.all(12.0),
        children: [
          AdminActionsSection(),
          <PERSON><PERSON><PERSON><PERSON>(height: 64.0),
          CurrencyControlSection(),
        ],
      ),
    );
  }
}
