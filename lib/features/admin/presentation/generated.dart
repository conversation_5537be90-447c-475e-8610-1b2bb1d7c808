import 'package:auto_route/annotations.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';
import 'package:tony_clou_app/core/gen/assets.gen.dart';
import 'package:tony_clou_app/features/qr/data/models/qr.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/card/index.dart';
import 'package:tony_clou_app/shared/widgets/containers/wrapper/index.dart';
import 'package:tony_clou_app/shared/widgets/interactive/elevated_button.dart';

@RoutePage()
class GeneratedQRScreen extends StatefulWidget {
  const GeneratedQRScreen({
    super.key,
    required this.data,
    required this.qrCode,
  });

  final String data;
  final QrCodeModel qrCode;

  @override
  State<GeneratedQRScreen> createState() => _GeneratedQRScreenState();
}

class _GeneratedQRScreenState extends State<GeneratedQRScreen> {
  final _screenShotController = ScreenshotController();

  Future<void> _shareQR() async {
    try {
      final capture = await _screenShotController.capture(
        delay: const Duration(milliseconds: 200),
      );
      if (capture == null) return;

      await SharePlus.instance.share(
        ShareParams(
          files: [
            XFile.fromData(capture, mimeType: 'image/png', name: 'QR.png'),
          ],
        ),
      );
    } catch (e) {
      print(e);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Ошибка при создании QR: ${e.toString()}')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Wrapper(
      appBar: CupertinoNavigationBar(
        automaticallyImplyLeading: true,
        previousPageTitle: 'Админка',
        middle: Text('Сгенерированный QR', style: Fonts.labelMedium),
      ),
      body: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Screenshot(
                controller: _screenShotController,
                child: CustomCard(
                  padding: EdgeInsets.all(32.0),
                  child: Column(
                    children: [
                      QrImageView(
                        padding: EdgeInsets.all(0.0),
                        data: widget.data,
                        errorCorrectionLevel: QrErrorCorrectLevel.H,
                      ),
                      SizedBox(height: 24.0),
                      if (widget.qrCode.moneyGem != null)
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '${widget.qrCode.moneyGem?.value}',
                              style: Fonts.headlineSmall,
                            ),
                            SizedBox(width: 8.0),
                            Text(
                              'Gems',
                              style: Fonts.headlineSmall.merge(
                                TextStyle(
                                  color:
                                      isDarkTheme
                                          ? AppColors.darkGem
                                          : AppColors.lightGem,
                                ),
                              ),
                            ),
                            SizedBox(width: 12.0),
                            Assets.icons.gem.svg(width: 24),
                          ],
                        ),
                      if (widget.qrCode.moneyGem != null &&
                          widget.qrCode.moneyGem!.expiresAt != null)
                        Text(
                          '${widget.qrCode.moneyGem!.expirationInDays} дн.',
                          style: Fonts.bodySmall.merge(
                            TextStyle(
                              color:
                                  isDarkTheme
                                      ? AppColors.darkDescription
                                      : AppColors.lightDescription,
                            ),
                          ),
                        ),
                      if (widget.qrCode.moneyCoin != null)
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              widget.qrCode.moneyCoin.toString(),
                              style: Fonts.headlineSmall,
                            ),
                            SizedBox(width: 8.0),
                            Text(
                              'Coins',
                              style: Fonts.headlineSmall.merge(
                                TextStyle(
                                  color:
                                      isDarkTheme
                                          ? AppColors.darkCoin
                                          : AppColors.lightCoin,
                                ),
                              ),
                            ),
                            SizedBox(width: 12.0),
                            Assets.icons.coin.svg(width: 24),
                          ],
                        ),
                      if (widget.qrCode.description != null)
                        SizedBox(height: 16.0),
                      if (widget.qrCode.description != null)
                        Text(
                          widget.qrCode.description!,
                          style: Fonts.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                      if (widget.qrCode.expiresAt != null)
                        SizedBox(height: 16.0),
                      if (widget.qrCode.expiresAt != null)
                        Text(
                          'QR до: ${DateFormat('dd.MM.yyyy').format(widget.qrCode.expiresAt!)}',
                          style: Fonts.bodySmall.merge(
                            TextStyle(
                              color:
                                  isDarkTheme
                                      ? AppColors.darkDescription
                                      : AppColors.lightDescription,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 16.0),
              SelectableText(
                widget.data,
                textAlign: TextAlign.center,
                style: Fonts.bodySmall.merge(
                  TextStyle(
                    color:
                        isDarkTheme
                            ? AppColors.darkDescription
                            : AppColors.lightDescription,
                  ),
                ),
              ),
              SizedBox(height: 16.0),
              CustomElevatedButton(text: 'Поделиться', onPressed: _shareQR),
            ],
          ),
        ),
      ),
    );
  }
}
