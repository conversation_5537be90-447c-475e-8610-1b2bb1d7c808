import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tony_clou_app/features/user/data/models/index.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';
import 'package:tony_clou_app/features/user/data/repository/index.dart';
import 'package:tony_clou_app/features/user/presentation/card.dart';
import 'package:tony_clou_app/shared/data/models/pagination.dart';
import 'package:tony_clou_app/shared/helpers/debouncer.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/wrapper/index.dart';

@RoutePage()
class UserListScreen extends StatefulWidget {
  const UserListScreen({super.key});

  @override
  State<UserListScreen> createState() => _UserListScreenState();
}

class _UserListScreenState extends State<UserListScreen> {
  List<User> _users = [];
  bool _isLoading = false;
  bool _isInitialLoading = true;
  int _currentPage = 1;
  final int _perPage = 20;
  bool _hasMore = true;
  String _currentQuery = '';

  final _debouncer = Debouncer();
  final _scrollController = ScrollController();
  final _searchController = TextEditingController();

  Future<void> _loadUsers({String? search, bool isRefresh = false}) async {
    if (_isLoading || (!_hasMore && !isRefresh)) return;

    // Update current query if search is provided
    if (search != null) {
      _currentQuery = search;
    }

    if (isRefresh) {
      setState(() {
        _currentPage = 1;
        _hasMore = true;
        _users = [];
        _isInitialLoading = _users.isEmpty;
      });
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await UserRepository.getUsers(
        GetUsersInput(
          filter:
              _currentQuery.isEmpty ? null : UserFilter(query: _currentQuery),
          pagination: Pagination(page: _currentPage, perPage: _perPage),
        ),
      );

      setState(() {
        if (result.data != null) {
          _users =
              isRefresh
                  ? result.data!.data ?? []
                  : [..._users, ...result.data!.data ?? []];
          _hasMore = (result.data!.data?.length ?? 0) == _perPage;
          _currentPage++;
        } else {
          _hasMore = false;
        }
        _isLoading = false;
        _isInitialLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _isInitialLoading = false;
        _hasMore = false;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _loadUsers();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent * 0.8) {
        _loadUsers();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Wrapper(
      appBar: CupertinoNavigationBar(
        automaticallyImplyLeading: true,
        previousPageTitle: 'Админка',
        middle: Text(
          'Пользователи (${_users.length})',
          style: Fonts.labelMedium,
        ),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(12.0, 12.0, 12.0, 0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Поиск пользователей...',
                prefixIcon: Icon(CupertinoIcons.search),
                suffixIcon:
                    _searchController.text.isNotEmpty
                        ? IconButton(
                          icon: Icon(CupertinoIcons.clear),
                          onPressed: () {
                            _searchController.clear();
                            _loadUsers(search: '', isRefresh: true);
                          },
                        )
                        : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.0),
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surface,
              ),
              onChanged: (value) {
                setState(() {}); // Для обновления suffixIcon
                _debouncer.run(
                  () => _loadUsers(search: value, isRefresh: true),
                );
              },
            ),
          ),
          Expanded(
            child:
                _isInitialLoading
                    ? Center(child: CircularProgressIndicator())
                    : RefreshIndicator.adaptive(
                      onRefresh: () => _loadUsers(isRefresh: true),
                      child:
                          _users.isEmpty && !_isLoading
                              ? ListView(
                                children: [
                                  SizedBox(height: 100),
                                  Center(
                                    child: Column(
                                      children: [
                                        Icon(
                                          CupertinoIcons.person_2,
                                          size: 64,
                                          color: Theme.of(context)
                                              .colorScheme
                                              .onSurface
                                              .withValues(alpha: 0.5),
                                        ),
                                        SizedBox(height: 16),
                                        Text(
                                          _currentQuery.isEmpty
                                              ? 'Нет пользователей'
                                              : 'Пользователи не найдены',
                                          style: Theme.of(
                                            context,
                                          ).textTheme.titleMedium?.copyWith(
                                            color: Theme.of(context)
                                                .colorScheme
                                                .onSurface
                                                .withValues(alpha: 0.7),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              )
                              : ListView.separated(
                                controller: _scrollController,
                                padding: EdgeInsets.all(12.0),
                                itemCount:
                                    _users.length +
                                    (_isLoading && !_isInitialLoading ? 1 : 0),
                                itemBuilder: (context, index) {
                                  if (index == _users.length && _isLoading) {
                                    return Padding(
                                      padding: EdgeInsets.all(16.0),
                                      child: Center(
                                        child: CircularProgressIndicator(),
                                      ),
                                    );
                                  }

                                  final user = _users[index];
                                  return UserCard(data: user);
                                },
                                separatorBuilder:
                                    (context, index) => SizedBox(height: 8.0),
                              ),
                    ),
          ),
        ],
      ),
    );
  }
}
