import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:tony_clou_app/core/navigation/index.gr.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/card/index.dart';

class AdminActionsSection extends StatelessWidget {
  const AdminActionsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _AdminActionCard(
          title: 'Список пользователей',
          onTap: () {
            context.router.push(UserListRoute());
          },
        ),
        const SizedBox(height: 12.0),
        _AdminActionCard(
          title: 'Чеки',
          onTap: () {
            context.router.push(ChecksListRoute());
          },
        ),
      ],
    );
  }
}

class _AdminActionCard extends StatelessWidget {
  final String title;
  final void Function() onTap;

  const _AdminActionCard({required this.title, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      onTap: onTap,
      child: Row(
        children: [
          Text(title, style: Fonts.labelMedium),
          const Spacer(),
          const Icon(CupertinoIcons.forward),
        ],
      ),
    );
  }
}
