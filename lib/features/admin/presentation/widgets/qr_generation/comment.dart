import 'package:flutter/material.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';

class CommentInputField extends StatelessWidget {
  final TextEditingController controller;

  const CommentInputField({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      decoration: const InputDecoration(labelText: 'Комментарий'),
      controller: controller,
      style: Fonts.labelSmall,
    );
  }
}
