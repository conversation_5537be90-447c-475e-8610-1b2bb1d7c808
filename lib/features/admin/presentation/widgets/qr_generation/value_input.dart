import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';

class ValueInputField extends StatelessWidget {
  final TextEditingController controller;
  final bool isMinus;
  final VoidCallback onToggleSign;

  const ValueInputField({
    super.key,
    required this.controller,
    required this.isMinus,
    required this.onToggleSign,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        IconButton(
          onPressed: onToggleSign,
          icon: Icon(isMinus ? CupertinoIcons.minus : CupertinoIcons.add),
        ),
        const SizedBox(width: 8.0),
        Expanded(
          child: TextFormField(
            decoration: const InputDecoration(labelText: 'Значение'),
            controller: controller,
            style: Fonts.labelSmall,
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            validator: (value) {
              if (value == null || value.isEmpty) return 'Введите значение';
              if (int.tryParse(value) == null) {
                return 'Введите корректное число';
              }
              if (int.parse(value) <= 0) return 'Значение должно быть больше 0';
              return null;
            },
          ),
        ),
      ],
    );
  }
}
