import 'package:auto_route/auto_route.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tony_clou_app/core/navigation/index.gr.dart';
import 'package:tony_clou_app/features/admin/presentation/widgets/qr_generation/comment.dart';
import 'package:tony_clou_app/features/admin/presentation/widgets/qr_generation/duration_selectors.dart';
import 'package:tony_clou_app/features/admin/presentation/widgets/qr_generation/type.dart';
import 'package:tony_clou_app/features/admin/presentation/widgets/qr_generation/value_input.dart';
import 'package:tony_clou_app/features/qr/data/models/index.dart';
import 'package:tony_clou_app/features/qr/data/models/qr.dart';
import 'package:tony_clou_app/features/qr/data/repository/index.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/interactive/elevated_button.dart';

class CurrencyControlSection extends StatefulWidget {
  const CurrencyControlSection({super.key});

  @override
  State<CurrencyControlSection> createState() => _CurrencyControlSectionState();
}

class _CurrencyControlSectionState extends State<CurrencyControlSection> {
  UserMoneyEnum _selectedMoneyType = UserMoneyEnum.coins;
  bool _isMinus = false;
  Duration _duration = const Duration(days: 1);
  Duration _gemDuration = const Duration(days: 7);
  final TextEditingController _valueController = TextEditingController(
    text: '100',
  );
  final TextEditingController _commentController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  void dispose() {
    _valueController.dispose();
    _commentController.dispose();
    super.dispose();
  }

  Future<Response<CreateQrOutput>?> _createQr() async {
    setState(() {
      _isLoading = true;
    });

    final value =
        (_isMinus ? -1 : 1) * (int.tryParse(_valueController.text) ?? 0);

    final result = await QrRepostory.createQr(
      QrCodeModel(
        moneyCoin: _selectedMoneyType == UserMoneyEnum.coins ? value : 0,
        moneyGem:
            _selectedMoneyType == UserMoneyEnum.gems
                ? UserMoneyGem(
                  value: value,
                  expirationInDays: _isMinus ? null : _gemDuration.inDays,
                )
                : null,
        description: _commentController.text,
        expiresAt: DateTime.now().add(_duration),
      ),
    );

    setState(() {
      _isLoading = false;
    });

    return result;
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Tony Clou Coins / Gems', style: Fonts.titleMedium),
          const SizedBox(height: 32.0),
          ValueInputField(
            controller: _valueController,
            isMinus: _isMinus,
            onToggleSign: () => setState(() => _isMinus = !_isMinus),
          ),
          const SizedBox(height: 16.0),
          CurrencyTypeSelector(
            selectedType: _selectedMoneyType,
            onTypeSelected: (type) => setState(() => _selectedMoneyType = type),
          ),
          const SizedBox(height: 16.0),
          DurationSelectors(
            duration: _duration,
            gemDuration: _gemDuration,
            showGemDuration:
                _selectedMoneyType == UserMoneyEnum.gems && !_isMinus,
            onDurationChanged:
                (duration) => setState(() => _duration = duration),
            onGemDurationChanged:
                (duration) => setState(() => _gemDuration = duration),
            isDarkTheme: isDarkTheme,
          ),
          const SizedBox(height: 32.0),
          CommentInputField(controller: _commentController),
          const SizedBox(height: 32.0),
          CustomElevatedButton(
            onPressed: () async {
              if (_formKey.currentState?.validate() ?? false) {
                // get qr id and redirect if success
                final result = await _createQr();
                if (result?.data?.id != null &&
                    result?.data?.qrCode != null &&
                    mounted) {
                  context.router.push(
                    GeneratedQRRoute(
                      data: result!.data!.id!,
                      qrCode: result.data!.qrCode!,
                    ),
                  );
                }
              }
            },
            text: 'Сгенерировать QR',
            child: _isLoading ? CupertinoActivityIndicator() : null,
          ),
          const SizedBox(height: 12.0),
          Text(
            'Количество использований QR кода: 1',
            textAlign: TextAlign.center,
            style: Fonts.bodySmall.merge(
              TextStyle(
                color:
                    isDarkTheme
                        ? AppColors.darkDescription
                        : AppColors.lightDescription,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
