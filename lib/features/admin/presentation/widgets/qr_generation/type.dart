import 'package:flutter/cupertino.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';
import 'package:tony_clou_app/shared/widgets/interactive/elevated_button.dart';

class CurrencyTypeSelector extends StatelessWidget {
  final UserMoneyEnum selectedType;
  final ValueChanged<UserMoneyEnum> onTypeSelected;

  const CurrencyTypeSelector({
    super.key,
    required this.selectedType,
    required this.onTypeSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: CustomElevatedButton(
            type:
                selectedType == UserMoneyEnum.coins
                    ? CustomElevatedButtonTypes.coins
                    : CustomElevatedButtonTypes.common,
            onPressed: () => onTypeSelected(UserMoneyEnum.coins),
            text: 'Coins',
          ),
        ),
        const SizedBox(width: 8.0),
        Expanded(
          child: CustomElevatedButton(
            type:
                selectedType == UserMoneyEnum.gems
                    ? CustomElevatedButtonTypes.gems
                    : CustomElevatedButtonTypes.common,
            onPressed: () => onTypeSelected(UserMoneyEnum.gems),
            text: 'Gems',
          ),
        ),
      ],
    );
  }
}
