import 'package:flutter/cupertino.dart';
import 'package:tony_clou_app/shared/widgets/interactive/pickers/button.dart';

class DurationSelectors extends StatelessWidget {
  final Duration duration;
  final Duration gemDuration;
  final bool showGemDuration;
  final ValueChanged<Duration> onDurationChanged;
  final ValueChanged<Duration> onGemDurationChanged;
  final bool isDarkTheme;

  const DurationSelectors({
    super.key,
    required this.duration,
    required this.gemDuration,
    required this.showGemDuration,
    required this.onDurationChanged,
    required this.onGemDurationChanged,
    required this.isDarkTheme,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: DurationPicker(
            duration: duration,
            label: 'Время действия QR',
            onDurationChanged: onDurationChanged,
            isDarkTheme: isDarkTheme,
          ),
        ),
        if (showGemDuration) const SizedBox(width: 12.0),
        if (showGemDuration)
          Expanded(
            child: DurationPicker(
              duration: gemDuration,
              label: 'Время действия Gems',
              onDurationChanged: onGemDurationChanged,
              isDarkTheme: isDarkTheme,
            ),
          ),
      ],
    );
  }
}
