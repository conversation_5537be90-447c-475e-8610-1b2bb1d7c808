import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tony_clou_app/core/navigation/index.gr.dart';
import 'package:tony_clou_app/features/authorization/data/repository/index.dart';
import 'package:tony_clou_app/features/authorization/presentation/bloc/index.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/wrapper/index.dart';
import 'package:tony_clou_app/shared/widgets/interactive/elevated_button.dart';

@RoutePage()
class RegistationScreen extends StatefulWidget {
  const RegistationScreen({super.key});

  @override
  State<RegistationScreen> createState() => _RegistationScreenState();
}

class _RegistationScreenState extends State<RegistationScreen> {
  bool _isLoading = false;
  final _emailController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    final email = context.read<BlocAuthorization>().state.email;
    _emailController.text = email ?? '';
  }

  Future<bool> _enter() async {
    setState(() {
      _isLoading = true;
    });

    final result = await AuthRepository.emailVerify(
      email: _emailController.text,
    );
    final ok = result.statusCode == 202;

    setState(() {
      _isLoading = false;
    });

    return ok;
  }

  @override
  Widget build(BuildContext context) {
    return Wrapper(
      appBar: CupertinoNavigationBar(
        automaticallyImplyLeading: false,
        middle: Text('Регистрация', style: Fonts.labelMedium),
        previousPageTitle: '',
      ),
      body: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Center(
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Введите почту', style: Fonts.titleSmall),
                SizedBox(height: 32.0),
                TextFormField(
                  controller: _emailController,
                  decoration: InputDecoration(labelText: 'Email'),
                  keyboardType: TextInputType.emailAddress,
                  style: Fonts.labelSmall,
                ),
                SizedBox(height: 32.0),
                CustomElevatedButton(
                  onPressed: () async {
                    if (_formKey.currentState!.validate()) {
                      final ok = await _enter();
                      if (ok && mounted) {
                        context.read<BlocAuthorization>().add(
                          SetAuthEmail(_emailController.text),
                        );
                        context.router.push(RegistrationRouteStep2());
                      }
                    }
                  },
                  text: 'Далее ->',
                  child: _isLoading ? CupertinoActivityIndicator() : null,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
