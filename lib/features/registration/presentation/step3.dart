import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:intl/intl.dart';
import 'package:tony_clou_app/core/navigation/index.gr.dart';
import 'package:tony_clou_app/features/authorization/presentation/bloc/index.dart';
import 'package:tony_clou_app/features/main/presentation/bloc/user/index.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';
import 'package:tony_clou_app/features/user/data/repository/index.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/wrapper/index.dart';
import 'package:tony_clou_app/shared/widgets/interactive/elevated_button.dart';

@RoutePage()
class RegistrationScreenStep3 extends StatefulWidget {
  const RegistrationScreenStep3({super.key});

  @override
  State<RegistrationScreenStep3> createState() =>
      _RegistrationScreenStep3State();
}

class _RegistrationScreenStep3State extends State<RegistrationScreenStep3> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _surnameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  DateTime? _dateOfBirth;

  bool _isLoading = false;
  String? _errorMessage;

  final _formKey = GlobalKey<FormState>();

  // Валидация имени
  String? _validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Пожалуйста, введите имя';
    }
    if (value.length < 2) {
      return 'Имя слишком короткое';
    }
    if (!RegExp(r'^[а-яА-ЯёЁa-zA-Z\- ]+$').hasMatch(value)) {
      return 'Имя содержит недопустимые символы';
    }
    return null;
  }

  // Валидация фамилии
  String? _validateSurname(String? value) {
    if (value == null || value.isEmpty) {
      return 'Пожалуйста, введите фамилию';
    }
    if (!RegExp(r'^[а-яА-ЯёЁa-zA-Z\- ]+$').hasMatch(value)) {
      return 'Фамилия содержит недопустимые символы';
    }
    return null;
  }

  // Валидация телефона
  String? _validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'Пожалуйста, введите номер телефона';
    }
    if (value.length != 10) {
      return 'Номер телефона должен содержать 10 цифр';
    }
    if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
      return 'Номер телефона должен содержать только цифры';
    }
    return null;
  }

  // Валидация даты рождения
  String? _validateDateOfBirth() {
    if (_dateOfBirth == null) {
      return 'Пожалуйста, укажите дату рождения';
    }
    return null;
  }

  Future<void> _enter() async {
    if (!_formKey.currentState!.validate()) return;

    final dateError = _validateDateOfBirth();
    if (dateError != null) {
      setState(() {
        _errorMessage = dateError;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final state = context.read<BlocAuthorization>().state;

      final result = await UserRepository.create(
        User(
          name: _nameController.text.trim(),
          surname: _surnameController.text.trim(),
          email: state.email,
          telephoneNumber: '+7${_phoneController.text}',
          dateOfBirth: _dateOfBirth,
        ),
      );
      final data = result.data;

      // Переход на главный экран после успешной регистрации
      if (mounted && data?.user != null) {
        final prefs = FlutterSecureStorage();
        await prefs.write(key: 'accessToken', value: data?.accessToken);
        await prefs.write(key: 'refreshToken', value: data?.refreshToken);
        await prefs.write(key: 'deviceId', value: data?.deviceId);

        context.read<BlocUser>().add(SetSelf(data!.user!));
        context.router.replace(const MainRoute());
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Произошла ошибка: ${e.toString()}';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _surnameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final theme = Theme.of(context);

    return Wrapper(
      appBar: CupertinoNavigationBar(
        automaticallyImplyLeading: true,
        previousPageTitle: '',
        middle: Text('Регистрация', style: Fonts.labelMedium),
      ),
      body: Form(
        key: _formKey,
        child: Center(
          child: ListView(
            padding: const EdgeInsets.all(16.0),
            shrinkWrap: true,
            physics: const ClampingScrollPhysics(),
            children: [
              Text(
                'Как к вам обращаться?',
                style: Fonts.titleSmall.merge(
                  TextStyle(color: isDarkTheme ? Colors.white : Colors.black),
                ),
              ),
              const SizedBox(height: 32.0),

              // Поле имени
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(labelText: 'Имя', errorMaxLines: 2),
                style: Fonts.labelSmall,
                validator: _validateName,
                onChanged: (name) {
                  context.read<BlocAuthorization>().add(SetAuthName(name));
                },
                textCapitalization: TextCapitalization.words,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(
                    RegExp(r'[а-яА-ЯёЁa-zA-Z\- ]'),
                  ),
                ],
              ),
              const SizedBox(height: 16.0),

              // Поле фамилии
              TextFormField(
                controller: _surnameController,
                decoration: InputDecoration(
                  labelText: 'Фамилия',
                  errorMaxLines: 2,
                ),
                style: Fonts.labelSmall,
                validator: _validateSurname,
                onChanged: (surname) {
                  context.read<BlocAuthorization>().add(
                    SetAuthSurname(surname),
                  );
                },
                textCapitalization: TextCapitalization.words,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(
                    RegExp(r'[а-яА-ЯёЁa-zA-Z\- ]'),
                  ),
                ],
              ),
              const SizedBox(height: 16.0),

              // Поле даты рождения
              InkWell(
                borderRadius: BorderRadius.circular(20),
                onTap: _showDatePicker,
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: 'Дата рождения',
                    errorText:
                        _dateOfBirth == null &&
                                _errorMessage ==
                                    'Пожалуйста, укажите дату рождения'
                            ? _errorMessage
                            : null,
                    errorMaxLines: 2,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _dateOfBirth != null
                            ? DateFormat('dd.MM.yyyy').format(_dateOfBirth!)
                            : 'Выберите дату',
                        style: Fonts.labelSmall.merge(
                          TextStyle(
                            color:
                                _dateOfBirth != null
                                    ? (isDarkTheme
                                        ? Colors.white
                                        : Colors.black)
                                    : theme.hintColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16.0),

              // Поле телефона
              TextFormField(
                controller: _phoneController,
                decoration: InputDecoration(
                  labelText: 'Номер телефона',
                  counterText: '',
                  prefixText: '+7 ',
                  errorMaxLines: 2,
                ),
                style: Fonts.labelSmall,
                validator: _validatePhone,
                onChanged: (phoneNumber) {
                  context.read<BlocAuthorization>().add(
                    SetAuthPhoneNumber(phoneNumber),
                  );
                },
                maxLength: 10,
                keyboardType: TextInputType.phone,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(10),
                ],
              ),
              const SizedBox(height: 8.0),
              Text(
                'Номер телефона нужен как способ идентификации',
                style: Fonts.bodySmall.merge(
                  TextStyle(
                    color:
                        isDarkTheme
                            ? AppColors.darkDescription
                            : AppColors.lightDescription,
                  ),
                ),
              ),

              // Сообщение об ошибке
              if (_errorMessage != null &&
                  _errorMessage != 'Пожалуйста, укажите дату рождения')
                Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: Text(
                    _errorMessage!,
                    style: Fonts.bodySmall.merge(
                      TextStyle(
                        color:
                            isDarkTheme
                                ? AppColors.darkError
                                : AppColors.lightError,
                      ),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

              const SizedBox(height: 32.0),

              // Кнопка завершения регистрации
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : CustomElevatedButton(
                    type: CustomElevatedButtonTypes.accent,
                    onPressed: _enter,
                    text: 'Завершить регистрацию',
                  ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDatePicker() {
    final now = DateTime.now();
    DateTime lastDate = DateTime(now.year - 18, now.month, now.day);

    if (now.month < lastDate.month ||
        (now.month == lastDate.month && now.day < lastDate.day)) {
      lastDate = DateTime(now.year - 19, now.month, now.day);
    }

    showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) {
        final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

        return Container(
          height: 300,
          width: double.infinity,
          color:
              isDarkTheme
                  ? AppColors.darkBackground
                  : AppColors.lightBackground,
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'Готово',
                      style: TextStyle(
                        color:
                            isDarkTheme
                                ? AppColors.darkSecondary
                                : AppColors.lightSecondary,
                      ),
                    ),
                  ),
                ],
              ),
              Expanded(
                child: CupertinoDatePicker(
                  mode: CupertinoDatePickerMode.date,
                  initialDateTime: _dateOfBirth ?? lastDate,
                  minimumDate: DateTime(1900),
                  maximumDate: lastDate,
                  onDateTimeChanged: (DateTime newDateTime) {
                    setState(() {
                      _dateOfBirth = newDateTime;
                    });
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
