import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';

part 'events.dart';
part 'index.freezed.dart';
part 'state.dart';

class BlocUser extends Bloc<BlocUserEvents, BlocUserState> {
  BlocUser() : super(BlocUserState(self: User(), user: User())) {
    on<SetSelf>((event, emit) {
      emit(state.copyWith(self: event.user));
    });
    on<ClearSelf>((event, emit) {
      emit(state.copyWith(self: User()));
    });
    on<SetUser>((event, emit) {
      emit(state.copyWith(user: event.user));
    });
    on<ClearUser>((event, emit) {
      emit(state.copyWith(user: User()));
    });
  }

  static UserMoneyGem getCompressionGem(User user) {
    final now = DateTime.now().toUtc(); // Ensure UTC for comparison

    // Get all gems, even if empty
    final gems = user.money?.gems ?? [];

    // If no gems, return empty UserMoneyGem
    if (gems.isEmpty) {
      return UserMoneyGem(value: 0);
    }

    // Sum all values
    final totalValue = gems.fold<int>(0, (sum, gem) => sum + (gem.value ?? 0));

    // Find the closest future expiresAt date
    DateTime? closestFutureExpiresAt;
    for (var gem in gems) {
      if (gem.expiresAt != null && gem.expiresAt!.toUtc().isAfter(now)) {
        if (closestFutureExpiresAt == null ||
            gem.expiresAt!.toUtc().difference(now).inMilliseconds <
                closestFutureExpiresAt.difference(now).inMilliseconds) {
          closestFutureExpiresAt = gem.expiresAt;
        }
      }
    }

    return UserMoneyGem(value: totalValue, expiresAt: closestFutureExpiresAt);
  }

  static UserMoneyGem getCompressionGemFromList(List<UserMoneyGem> list) {
    final now = DateTime.now().toUtc(); // Ensure UTC for comparison

    // If no gems, return empty UserMoneyGem
    if (list.isEmpty) {
      return UserMoneyGem(value: 0);
    }

    // Sum all values
    final totalValue = list.fold<int>(0, (sum, gem) => sum + (gem.value ?? 0));

    // Find the closest future expiresAt date
    DateTime? closestFutureExpiresAt;
    for (var gem in list) {
      if (gem.expiresAt != null && gem.expiresAt!.toUtc().isAfter(now)) {
        if (closestFutureExpiresAt == null ||
            gem.expiresAt!.toUtc().difference(now).inMilliseconds <
                closestFutureExpiresAt.difference(now).inMilliseconds) {
          closestFutureExpiresAt = gem.expiresAt;
        }
      }
    }

    return UserMoneyGem(value: totalValue, expiresAt: closestFutureExpiresAt);
  }
}
