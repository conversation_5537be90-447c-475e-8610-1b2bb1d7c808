// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'index.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$BlocUserState {

 User get self; User get user;
/// Create a copy of BlocUserState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BlocUserStateCopyWith<BlocUserState> get copyWith => _$BlocUserStateCopyWithImpl<BlocUserState>(this as BlocUserState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BlocUserState&&(identical(other.self, self) || other.self == self)&&(identical(other.user, user) || other.user == user));
}


@override
int get hashCode => Object.hash(runtimeType,self,user);

@override
String toString() {
  return 'BlocUserState(self: $self, user: $user)';
}


}

/// @nodoc
abstract mixin class $BlocUserStateCopyWith<$Res>  {
  factory $BlocUserStateCopyWith(BlocUserState value, $Res Function(BlocUserState) _then) = _$BlocUserStateCopyWithImpl;
@useResult
$Res call({
 User self, User user
});


$UserCopyWith<$Res> get self;$UserCopyWith<$Res> get user;

}
/// @nodoc
class _$BlocUserStateCopyWithImpl<$Res>
    implements $BlocUserStateCopyWith<$Res> {
  _$BlocUserStateCopyWithImpl(this._self, this._then);

  final BlocUserState _self;
  final $Res Function(BlocUserState) _then;

/// Create a copy of BlocUserState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? self = null,Object? user = null,}) {
  return _then(_self.copyWith(
self: null == self ? _self.self : self // ignore: cast_nullable_to_non_nullable
as User,user: null == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as User,
  ));
}
/// Create a copy of BlocUserState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserCopyWith<$Res> get self {
  
  return $UserCopyWith<$Res>(_self.self, (value) {
    return _then(_self.copyWith(self: value));
  });
}/// Create a copy of BlocUserState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserCopyWith<$Res> get user {
  
  return $UserCopyWith<$Res>(_self.user, (value) {
    return _then(_self.copyWith(user: value));
  });
}
}


/// @nodoc


class _BlocUserState implements BlocUserState {
  const _BlocUserState({required this.self, required this.user});
  

@override final  User self;
@override final  User user;

/// Create a copy of BlocUserState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BlocUserStateCopyWith<_BlocUserState> get copyWith => __$BlocUserStateCopyWithImpl<_BlocUserState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BlocUserState&&(identical(other.self, self) || other.self == self)&&(identical(other.user, user) || other.user == user));
}


@override
int get hashCode => Object.hash(runtimeType,self,user);

@override
String toString() {
  return 'BlocUserState(self: $self, user: $user)';
}


}

/// @nodoc
abstract mixin class _$BlocUserStateCopyWith<$Res> implements $BlocUserStateCopyWith<$Res> {
  factory _$BlocUserStateCopyWith(_BlocUserState value, $Res Function(_BlocUserState) _then) = __$BlocUserStateCopyWithImpl;
@override @useResult
$Res call({
 User self, User user
});


@override $UserCopyWith<$Res> get self;@override $UserCopyWith<$Res> get user;

}
/// @nodoc
class __$BlocUserStateCopyWithImpl<$Res>
    implements _$BlocUserStateCopyWith<$Res> {
  __$BlocUserStateCopyWithImpl(this._self, this._then);

  final _BlocUserState _self;
  final $Res Function(_BlocUserState) _then;

/// Create a copy of BlocUserState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? self = null,Object? user = null,}) {
  return _then(_BlocUserState(
self: null == self ? _self.self : self // ignore: cast_nullable_to_non_nullable
as User,user: null == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as User,
  ));
}

/// Create a copy of BlocUserState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserCopyWith<$Res> get self {
  
  return $UserCopyWith<$Res>(_self.self, (value) {
    return _then(_self.copyWith(self: value));
  });
}/// Create a copy of BlocUserState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserCopyWith<$Res> get user {
  
  return $UserCopyWith<$Res>(_self.user, (value) {
    return _then(_self.copyWith(user: value));
  });
}
}

// dart format on
