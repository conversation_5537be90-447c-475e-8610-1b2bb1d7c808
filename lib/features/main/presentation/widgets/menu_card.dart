import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tony_clou_app/core/gen/assets.gen.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/card/index.dart';

// Константы для MenuCard
class _MenuCardConstants {
  static const double iconPadding = 8.0;
  static const double iconSize = 24.0;
  static const double iconRadius = 8.0;
  static const double spacing = 16.0;
  static const double tinySpacing = 2.0;
  static const double chevronSize = 16.0;
  static const double iconOpacity = 0.1;
}

class MenuCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final AssetGenImage image;
  final Color iconColor;
  final VoidCallback onTap;

  const MenuCard({
    super.key,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.image,
    required this.iconColor,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      border: Border.all(width: 0.0, color: Colors.transparent),
      onTap: onTap,
      padding: EdgeInsets.all(0.0),
      child: Row(
        children: [
          Expanded(
            child: Stack(
              alignment: Alignment.center,
              children: [
                // const SizedBox(width: _MenuCardConstants.spacing),
                // _buildIcon(),
                Positioned.fill(child: _buildBackgroundImage(context)),
                Positioned.fill(child: Container(color: Colors.black26)),
                _buildContent(context),
                // _buildChevron(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIcon() {
    return Container(
      padding: const EdgeInsets.all(_MenuCardConstants.iconPadding),
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: _MenuCardConstants.iconOpacity),
        borderRadius: BorderRadius.circular(_MenuCardConstants.iconRadius),
      ),
      child: Icon(icon, color: iconColor, size: _MenuCardConstants.iconSize),
    );
  }

  Widget _buildBackgroundImage(BuildContext context) {
    return image.image(fit: BoxFit.cover);
  }

  Widget _buildContent(BuildContext context) {
    // final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 28.0),
        Text(
          title,
          textAlign: TextAlign.center,
          style: Fonts.labelMedium.copyWith(
            color: AppColors.darkPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: _MenuCardConstants.tinySpacing),
        Text(
          subtitle,
          textAlign: TextAlign.center,
          style: Fonts.bodySmall.copyWith(color: Colors.white60),
        ),
        SizedBox(height: 28.0),
      ],
    );
  }

  Widget _buildChevron(BuildContext context) {
    return Icon(
      CupertinoIcons.chevron_forward,
      color: CupertinoColors.tertiaryLabel.resolveFrom(context),
      size: _MenuCardConstants.chevronSize,
    );
  }
}
