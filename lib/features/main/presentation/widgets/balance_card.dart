import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tony_clou_app/core/gen/assets.gen.dart';
import 'package:tony_clou_app/features/main/presentation/bloc/user/index.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/card/index.dart';

// Константы для BalanceCard
class _BalanceCardConstants {
  static const double iconPadding = 8.0;
  static const double iconSize = 24.0;
  static const double iconRadius = 8.0;
  static const double spacing = 16.0;
  static const double smallSpacing = 4.0;
  static const double tinySpacing = 2.0;
  static const double currencySpacing = 6.0;
  static const double currencyIconSize = 16.0;
  static const double chevronSize = 16.0;
  static const double iconOpacity = 0.1;
  static const String balanceTitle = 'Баланс';
  static const String balanceSubtitle = 'Просмотр баланса и истории';
}

class BalanceCard extends StatelessWidget {
  final User user;
  final VoidCallback onTap;

  const BalanceCard({super.key, required this.user, required this.onTap});

  @override
  Widget build(BuildContext context) {
    final gems = BlocUser.getCompressionGem(user);

    return CustomCard(
      onTap: onTap,
      border: Border.all(width: 0.0, color: Colors.transparent),
      padding: EdgeInsets.all(0.0),
      child: Row(
        children: [
          Expanded(
            child: Stack(
              alignment: Alignment.center,
              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // _buildIcon(),
                // const SizedBox(width: _BalanceCardConstants.spacing),
                Positioned.fill(
                  child: Assets.images.image3.image(fit: BoxFit.cover),
                ),
                Positioned.fill(child: Container(color: Colors.black26)),
                _buildContent(context, gems),
                // _buildChevron(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIcon() {
    return Container(
      padding: const EdgeInsets.all(_BalanceCardConstants.iconPadding),
      decoration: BoxDecoration(
        color: CupertinoColors.systemBlue.withValues(
          alpha: _BalanceCardConstants.iconOpacity,
        ),
        borderRadius: BorderRadius.circular(_BalanceCardConstants.iconRadius),
      ),
      child: const Icon(
        CupertinoIcons.money_dollar_circle,
        color: CupertinoColors.systemBlue,
        size: _BalanceCardConstants.iconSize,
      ),
    );
  }

  Widget _buildContent(BuildContext context, UserMoneyGem gems) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 28.0),
        const Text(
          _BalanceCardConstants.balanceTitle,
          style: TextStyle(
            fontSize: 16.0,
            color: AppColors.darkPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        // const SizedBox(height: _BalanceCardConstants.tinySpacing),
        // Text(
        //   _BalanceCardConstants.balanceSubtitle,
        //   style: Fonts.bodySmall.copyWith(color: Colors.white60),
        // ),
        const SizedBox(height: _BalanceCardConstants.smallSpacing),
        _buildBalanceInfo(gems),
        SizedBox(height: 28.0),
      ],
    );
  }

  Widget _buildBalanceInfo(UserMoneyGem gems) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildCurrencyInfo(
          '${user.money?.coins ?? 0}',
          Assets.icons.coin.svg(width: _BalanceCardConstants.currencyIconSize),
          AppColors.lightCoin,
        ),
        const SizedBox(width: _BalanceCardConstants.currencySpacing),
        _buildCurrencyInfo(
          '${gems.value ?? 0}',
          Assets.icons.gem.svg(width: _BalanceCardConstants.currencyIconSize),
          AppColors.darkGem,
        ),
      ],
    );
  }

  Widget _buildCurrencyInfo(String value, Widget icon, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(value, style: Fonts.labelSmall.merge(TextStyle(color: color))),
        const SizedBox(width: _BalanceCardConstants.smallSpacing),
        icon,
      ],
    );
  }

  Widget _buildChevron(BuildContext context) {
    return Icon(
      CupertinoIcons.chevron_forward,
      color: CupertinoColors.tertiaryLabel.resolveFrom(context),
      size: _BalanceCardConstants.chevronSize,
    );
  }
}
