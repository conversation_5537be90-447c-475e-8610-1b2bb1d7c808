import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:tony_clou_app/core/gen/assets.gen.dart';
import 'package:tony_clou_app/core/navigation/index.gr.dart';
import 'package:tony_clou_app/features/main/presentation/bloc/user/index.dart';
import 'package:tony_clou_app/features/main/presentation/models/menu_item.dart';
import 'package:tony_clou_app/features/main/presentation/widgets/balance_card.dart';
import 'package:tony_clou_app/features/main/presentation/widgets/menu_card.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';
import 'package:tony_clou_app/shared/constants/ui_constants.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';

// Константы для MainScreen
// class _MainScreenConstants {
//   static const String heroTag = 'main-nav-bar';
// }

@RoutePage()
class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  PackageInfo? _appInfo;
  List<MenuItem>? _cachedMenuItems;
  bool? _cachedIsAdmin;

  void _getVersion() async {
    final PackageInfo appInfo = await PackageInfo.fromPlatform();
    if (mounted) {
      setState(() {
        _appInfo = appInfo;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _getVersion();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BlocUser, BlocUserState>(
      builder: (context, state) {
        final isAdmin = state.self.role == UserRole.admin;

        // Кэшируем меню если роль не изменилась
        if (_cachedIsAdmin != isAdmin || _cachedMenuItems == null) {
          _cachedMenuItems = _getMenuItems(context, isAdmin);
          _cachedIsAdmin = isAdmin;
        }

        return Scaffold(
          appBar: _buildNavigationBar(context),
          body: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            padding: const EdgeInsets.all(UIConstants.paddingLarge),
            child: Column(
              children: [
                // const SizedBox(height: UIConstants.paddingLarge),
                // _buildBanner(),

                // Balance card
                BalanceCard(
                  user: state.self,
                  onTap: () => context.router.push(BalanceRoute()),
                ),
                const SizedBox(height: UIConstants.paddingLarge),

                // Menu items
                ..._buildMenuItems(_cachedMenuItems!),

                const SizedBox(height: UIConstants.paddingMassive),
                _buildVersionText(context),
                const SizedBox(height: UIConstants.paddingXXLarge),
              ],
            ),
          ),
        );
      },
    );
  }

  PreferredSizeWidget _buildNavigationBar(BuildContext context) {
    return CupertinoNavigationBar(
      automaticallyImplyLeading: false,
      backgroundColor: CupertinoColors.systemBackground.resolveFrom(context),
      previousPageTitle: '',
      border: Border(
        bottom: BorderSide(
          color: CupertinoColors.separator.resolveFrom(context),
          width: 0.5,
        ),
      ),
      middle: const Text(
        AppTexts.appTitle,
        style: TextStyle(fontSize: 17.0, fontWeight: FontWeight.w600),
      ),
      // heroTag: _MainScreenConstants.heroTag,
      // transitionBetweenRoutes: false,
    );
  }

  List<Widget> _buildMenuItems(List<MenuItem> menuItems) {
    return menuItems.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;

      return Column(
        children: [
          if (index > 0) const SizedBox(height: UIConstants.paddingLarge),
          MenuCard(
            title: item.title,
            subtitle: item.subtitle,
            icon: item.icon,
            image: item.image,
            iconColor: item.iconColor,
            onTap: item.onTap,
          ),
        ],
      );
    }).toList();
  }

  Widget _buildBanner() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(UIConstants.radiusXLarge),
      child: Assets.images.banner.image(
        height: UIConstants.bannerHeight,
        width: double.infinity,
        fit: BoxFit.cover,
      ),
    );
  }

  Widget _buildVersionText(BuildContext context) {
    if (_appInfo?.version == null) return const SizedBox.shrink();

    return Text(
      'v. ${_appInfo!.version}',
      textAlign: TextAlign.center,
      style: Fonts.bodySmall.merge(
        TextStyle(
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? AppColors.darkDescription
                  : AppColors.lightDescription,
        ),
      ),
    );
  }

  List<MenuItem> _getMenuItems(BuildContext context, bool isAdmin) {
    final user = context.read<BlocUser>().state.self;

    final items = [
      MenuItem(
        title: 'Профиль',
        subtitle: '${user.surname} ${user.name}',
        icon: CupertinoIcons.person,
        image: Assets.images.image5,
        iconColor: CupertinoColors.systemIndigo,
        onTap: () => context.router.push(UserRoute()),
      ),
      MenuItem(
        title: AppTexts.locationsTitle,
        subtitle: AppTexts.locationsSubtitle,
        icon: CupertinoIcons.location_solid,
        image: Assets.images.image6,
        iconColor: CupertinoColors.systemBlue,
        onTap: () => context.router.push(LocationsRoute()),
      ),
      MenuItem(
        title: AppTexts.aboutTitle,
        subtitle: AppTexts.aboutSubtitle,
        icon: CupertinoIcons.info_circle,
        image: Assets.images.image7,
        iconColor: CupertinoColors.systemGreen,
        onTap: () => context.router.push(AboutRoute()),
      ),
      MenuItem(
        title: AppTexts.contactsTitle,
        subtitle: AppTexts.contactsSubtitle,
        icon: CupertinoIcons.phone,
        image: Assets.images.image4,
        iconColor: CupertinoColors.systemOrange,
        onTap: () => context.router.push(ContactsRoute()),
      ),
    ];

    if (isAdmin) {
      items.add(
        MenuItem(
          title: AppTexts.adminTitle,
          subtitle: AppTexts.adminSubtitle,
          icon: CupertinoIcons.lock_shield,
          image: Assets.images.image1,
          iconColor: CupertinoColors.systemRed,
          onTap: () => context.router.push(AdminRoute()),
          isAdminOnly: true,
        ),
      );
    }

    return items;
  }
}
