import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tony_clou_app/core/gen/assets.gen.dart';

class MenuItem {
  final String title;
  final String subtitle;
  final IconData icon;
  final AssetGenImage image;
  final Color iconColor;
  final VoidCallback onTap;
  final bool isAdminOnly;

  const MenuItem({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.image,
    required this.iconColor,
    required this.onTap,
    this.isAdminOnly = false,
  });
}
