import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:tony_clou_app/core/gen/assets.gen.dart';
import 'package:tony_clou_app/features/main/presentation/bloc/user/index.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/card/index.dart';

class MoneyHistoryCard extends StatelessWidget {
  const MoneyHistoryCard({super.key, required this.data});

  final MoneyHistory data;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return CustomCard(
      child: Column(
        children: [
          Row(
            children: [
              if (data.coins != null) ...[
                if (data.coins != null && !data.coins!.isNegative)
                  Icon(
                    CupertinoIcons.chevron_up,
                    color:
                        isDarkTheme
                            ? AppColors.darkSuccess
                            : AppColors.lightSuccess,
                  ),
                if (data.coins != null && data.coins!.isNegative)
                  Icon(
                    CupertinoIcons.chevron_down,
                    color:
                        isDarkTheme
                            ? AppColors.darkError
                            : AppColors.lightError,
                  ),

                SizedBox(width: 12.0),
                Text('${data.coins} TC Coins', style: Fonts.labelMedium),
                SizedBox(width: 8.0),
                Assets.icons.coin.svg(width: 24.0),
              ] else if (data.gems?.first != null) ...[
                if (!data.gems!.first.value!.isNegative)
                  Icon(
                    CupertinoIcons.chevron_up,
                    color:
                        isDarkTheme
                            ? AppColors.darkSuccess
                            : AppColors.lightSuccess,
                  ),
                if (data.gems!.first.value!.isNegative)
                  Icon(
                    CupertinoIcons.chevron_down,
                    color:
                        isDarkTheme
                            ? AppColors.darkError
                            : AppColors.lightError,
                  ),

                SizedBox(width: 12.0),
                Text(
                  '${BlocUser.getCompressionGemFromList(data.gems ?? []).value} TC Gems',
                  style: Fonts.labelMedium,
                ),
                SizedBox(width: 8.0),
                Assets.icons.gem.svg(width: 24.0),
              ],
            ],
          ),
          if (data.description != null) SizedBox(height: 4.0),
          if (data.description != null)
            Row(
              children: [
                Expanded(
                  child: Text(data.description!, style: Fonts.bodyMedium),
                ),
              ],
            ),
          if (data.timestamp != null) SizedBox(height: 4.0),
          if (data.timestamp != null)
            Row(
              children: [
                Expanded(
                  child: Text(
                    DateFormat(
                      'dd.MM.yyyy, HH:mm',
                    ).format(data.timestamp!.toLocal()),
                    style: Fonts.bodySmall.merge(
                      TextStyle(
                        color:
                            isDarkTheme
                                ? AppColors.darkDescription
                                : AppColors.lightDescription,
                      ),
                    ),
                    textAlign: TextAlign.end,
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }
}
