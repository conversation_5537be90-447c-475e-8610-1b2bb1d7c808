import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:tony_clou_app/core/gen/assets.gen.dart';
import 'package:tony_clou_app/features/main/presentation/bloc/user/index.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/card/index.dart';

// Константы для оптимизации
class _HistoryCardConstants {
  static const double iconSize = 24.0;
  static const double iconPadding = 8.0;
  static const double iconRadius = 8.0;
  static const double spacing = 12.0;
  static const double smallSpacing = 8.0;
  static const double tinySpacing = 4.0;
  static const double iconOpacity = 0.1;

  // Кэшированные форматтеры
  static final DateFormat _dateFormatter = DateFormat('dd.MM.yyyy, HH:mm');

  static String formatDate(DateTime date) =>
      _dateFormatter.format(date.toLocal());
}

// Оптимизированная карточка истории транзакций
class OptimizedMoneyHistoryCard extends StatelessWidget {
  const OptimizedMoneyHistoryCard({super.key, required this.data});

  final MoneyHistory data;

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: CustomCard(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildMainRow(context),
            if (data.description != null) ...[
              const SizedBox(height: _HistoryCardConstants.tinySpacing),
              _buildDescriptionRow(),
            ],
            if (data.timestamp != null) ...[
              const SizedBox(height: _HistoryCardConstants.tinySpacing),
              _buildTimestampRow(context),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMainRow(BuildContext context) {
    if (data.coins != null) {
      return _buildCoinsRow();
    } else if (data.gems?.isNotEmpty == true) {
      return _buildGemsRow();
    }
    return const SizedBox.shrink();
  }

  Widget _buildCoinsRow() {
    final isPositive = data.coins != null && !data.coins!.isNegative;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _OptimizedIcon(
          icon:
              isPositive
                  ? CupertinoIcons.chevron_up
                  : CupertinoIcons.chevron_down,
          color: isPositive ? AppColors.darkSuccess : AppColors.darkError,
        ),
        const SizedBox(width: _HistoryCardConstants.spacing),
        Text('${data.coins} TC Coins', style: Fonts.labelMedium),
        const SizedBox(width: _HistoryCardConstants.smallSpacing),
        _OptimizedCoinIcon(),
      ],
    );
  }

  Widget _buildGemsRow() {
    final gems = BlocUser.getCompressionGemFromList(data.gems ?? []);
    final isPositive = gems.value != null && !gems.value!.isNegative;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _OptimizedIcon(
          icon:
              isPositive
                  ? CupertinoIcons.chevron_up
                  : CupertinoIcons.chevron_down,
          color: isPositive ? AppColors.darkSuccess : AppColors.darkError,
        ),
        const SizedBox(width: _HistoryCardConstants.spacing),
        Text('${gems.value} TC Gems', style: Fonts.labelMedium),
        const SizedBox(width: _HistoryCardConstants.smallSpacing),
        _OptimizedGemIcon(),
      ],
    );
  }

  Widget _buildDescriptionRow() {
    return Row(
      children: [
        Expanded(
          child: Text(
            data.description!,
            style: Fonts.bodyMedium,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildTimestampRow(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Row(
      children: [
        Expanded(
          child: Text(
            _HistoryCardConstants.formatDate(data.timestamp!),
            style: Fonts.bodySmall.merge(
              TextStyle(
                color:
                    isDarkTheme
                        ? AppColors.darkDescription
                        : AppColors.lightDescription,
              ),
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }
}

// Оптимизированная иконка с кэшированием
class _OptimizedIcon extends StatelessWidget {
  const _OptimizedIcon({required this.icon, required this.color});

  final IconData icon;
  final Color color;

  static final Map<String, Widget> _iconCache = {};

  @override
  Widget build(BuildContext context) {
    final cacheKey = '${icon.codePoint}_${color.toARGB32()}';

    return _iconCache[cacheKey] ??= RepaintBoundary(
      child: Container(
        padding: const EdgeInsets.all(_HistoryCardConstants.iconPadding),
        decoration: BoxDecoration(
          color: color.withValues(alpha: _HistoryCardConstants.iconOpacity),
          borderRadius: BorderRadius.circular(_HistoryCardConstants.iconRadius),
        ),
        child: Icon(icon, color: color, size: _HistoryCardConstants.iconSize),
      ),
    );
  }
}

// Оптимизированная иконка монеты
class _OptimizedCoinIcon extends StatelessWidget {
  static Widget? _cachedIcon;

  @override
  Widget build(BuildContext context) {
    return _cachedIcon ??= RepaintBoundary(
      child: Assets.icons.coin.svg(width: _HistoryCardConstants.iconSize),
    );
  }
}

// Оптимизированная иконка гема
class _OptimizedGemIcon extends StatelessWidget {
  static Widget? _cachedIcon;

  @override
  Widget build(BuildContext context) {
    return _cachedIcon ??= RepaintBoundary(
      child: Assets.icons.gem.svg(width: _HistoryCardConstants.iconSize),
    );
  }
}

// Мемоизированная версия карточки для списков
class MemoizedMoneyHistoryCard extends StatelessWidget {
  const MemoizedMoneyHistoryCard({super.key, required this.data});

  final MoneyHistory data;

  static final Map<String, Widget> _cardCache = {};

  @override
  Widget build(BuildContext context) {
    // Создаем уникальный ключ на основе данных
    final cacheKey = _generateCacheKey();

    return _cardCache[cacheKey] ??= OptimizedMoneyHistoryCard(data: data);
  }

  String _generateCacheKey() {
    final buffer = StringBuffer();
    buffer.write(data.id ?? '');
    buffer.write('_');
    buffer.write(data.coins ?? '');
    buffer.write('_');
    buffer.write(data.gems?.first.value ?? '');
    buffer.write('_');
    buffer.write(data.description ?? '');
    buffer.write('_');
    buffer.write(data.timestamp?.millisecondsSinceEpoch ?? '');
    return buffer.toString();
  }

  // Метод для очистки кэша (вызывать при необходимости)
  static void clearCache() {
    _cardCache.clear();
    _OptimizedIcon._iconCache.clear();
    _OptimizedCoinIcon._cachedIcon = null;
    _OptimizedGemIcon._cachedIcon = null;
  }
}
