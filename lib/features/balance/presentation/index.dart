import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tony_clou_app/core/gen/assets.gen.dart';
import 'package:tony_clou_app/core/navigation/index.gr.dart';
import 'package:tony_clou_app/features/balance/presentation/optimized_history_card.dart';
import 'package:tony_clou_app/features/main/presentation/bloc/user/index.dart';
import 'package:tony_clou_app/features/user/data/models/index.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';
import 'package:tony_clou_app/features/user/data/repository/index.dart';
import 'package:tony_clou_app/shared/constants/ui_constants.dart';
import 'package:tony_clou_app/shared/data/models/pagination.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/card/index.dart';
import 'package:tony_clou_app/shared/widgets/interactive/ghost_button.dart';

// Оптимизированные константы
class _BalanceScreenConstants {
  static const int itemsPerPage = 20;
  static const int preloadThreshold = 200;
  static const int cacheSize = 50;
  static const double headerSpacing = 20.0;
  static const double actionButtonSpacing = 12.0;
  static const double historyItemSpacing = 12.0;
  static const double userAvatarSize = 20.0;
  static const double currencyIconSize = 20.0;
  static const double bottomPadding = 20.0;
  static const double adminBottomPadding = 80.0;
  static const String screenTitle = 'Баланс';
  static const String scanQrText = 'Скан QR';
  static const String addCheckText = 'Добавить чек';
  static const String noMoreDataText = 'Больше нет данных';
  static const String errorText = 'Ошибка загрузки';
  static const String emptyHistoryText = 'История транзакций пуста';
  static const String retryText = 'Повторить';
}

// Состояния загрузки
enum LoadingState { initial, loading, loaded, loadingMore, error, empty }

// Оптимизированная модель состояния
class _BalanceDataState {
  final LoadingState loadingState;
  final List<MoneyHistory> historyItems;
  final String? errorMessage;
  final bool hasMoreData;
  final int currentPage;
  final Map<int, List<MoneyHistory>> _cache;

  const _BalanceDataState({
    this.loadingState = LoadingState.initial,
    this.historyItems = const [],
    this.errorMessage,
    this.hasMoreData = true,
    this.currentPage = 1,
    Map<int, List<MoneyHistory>>? cache,
  }) : _cache = cache ?? const {};

  _BalanceDataState copyWith({
    LoadingState? loadingState,
    List<MoneyHistory>? historyItems,
    String? errorMessage,
    bool? hasMoreData,
    int? currentPage,
    Map<int, List<MoneyHistory>>? cache,
  }) {
    return _BalanceDataState(
      loadingState: loadingState ?? this.loadingState,
      historyItems: historyItems ?? this.historyItems,
      errorMessage: errorMessage ?? this.errorMessage,
      hasMoreData: hasMoreData ?? this.hasMoreData,
      currentPage: currentPage ?? this.currentPage,
      cache: cache ?? _cache,
    );
  }

  bool get isLoading => loadingState == LoadingState.loading;
  bool get isLoadingMore => loadingState == LoadingState.loadingMore;
  bool get hasError => loadingState == LoadingState.error;
  bool get isEmpty => loadingState == LoadingState.empty;
  bool get isLoaded => loadingState == LoadingState.loaded;

  List<MoneyHistory>? getCachedPage(int page) => _cache[page];

  _BalanceDataState withCachedPage(int page, List<MoneyHistory> items) {
    final newCache = Map<int, List<MoneyHistory>>.from(_cache);
    newCache[page] = items;

    if (newCache.length > _BalanceScreenConstants.cacheSize) {
      final oldestKey = newCache.keys.first;
      newCache.remove(oldestKey);
    }

    return copyWith(cache: newCache);
  }
}

// Оптимизированная кнопка действия
class _OptimizedActionButton extends StatelessWidget {
  const _OptimizedActionButton({
    required this.text,
    required this.icon,
    required this.onTap,
  });

  final String text;
  final IconData icon;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: CustomCard(
        onTap: onTap,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(text, style: Fonts.labelSmall),
            const SizedBox(width: UIConstants.paddingSmall),
            Icon(icon),
          ],
        ),
      ),
    );
  }
}

// Оптимизированный виджет баланса
class _OptimizedBalanceWidget extends StatelessWidget {
  const _OptimizedBalanceWidget({
    required this.coins,
    required this.gems,
    required this.coinColor,
    required this.gemColor,
  });

  final int coins;
  final int gems;
  final Color coinColor;
  final Color gemColor;

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Coins
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '$coins',
                style: Fonts.labelMedium.merge(
                  TextStyle(color: coinColor, fontWeight: FontWeight.w600),
                ),
              ),
              const SizedBox(width: UIConstants.paddingSmall),
              Assets.icons.coin.svg(
                width: _BalanceScreenConstants.currencyIconSize,
              ),
            ],
          ),
          const SizedBox(width: UIConstants.paddingSmall),
          // Gems
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '$gems',
                style: Fonts.labelMedium.merge(
                  TextStyle(color: gemColor, fontWeight: FontWeight.w600),
                ),
              ),
              const SizedBox(width: UIConstants.paddingSmall),
              Assets.icons.gem.svg(
                width: _BalanceScreenConstants.currencyIconSize,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// Оптимизированный виджет пользователя
class _OptimizedUserWidget extends StatelessWidget {
  const _OptimizedUserWidget({required this.user, required this.onTap});

  final User user;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: GhostButton(
        borderRadius: UIConstants.radiusLarge,
        backgroundOffset: const Offset(-4, -4),
        onTap: onTap,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Assets.icons.userAvatar.svg(
              width: _BalanceScreenConstants.userAvatarSize,
            ),
            const SizedBox(width: UIConstants.paddingMedium),
            Flexible(
              child: Text(
                '${user.surname ?? 'surname'} ${user.name ?? 'name'}',
                style: Fonts.labelSmall.merge(
                  const TextStyle(fontWeight: FontWeight.w500),
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

@RoutePage()
class BalanceScreen extends StatefulWidget {
  const BalanceScreen({super.key});

  @override
  State<BalanceScreen> createState() => _BalanceScreenState();
}

class _BalanceScreenState extends State<BalanceScreen>
    with AutomaticKeepAliveClientMixin, TickerProviderStateMixin {
  // Оптимизированное состояние с ValueNotifier
  late final ValueNotifier<_BalanceDataState> _dataStateNotifier;
  late final ScrollController _scrollController;

  // Кэш для мемоизации виджетов
  final Map<String, Widget> _widgetCache = {};

  // Дебаунсер для скролла
  Timer? _scrollDebouncer;

  // Флаг для предотвращения множественных запросов
  bool _isRequestInProgress = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _dataStateNotifier = ValueNotifier(const _BalanceDataState());
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
    _loadInitialData();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _dataStateNotifier.dispose();
    _scrollDebouncer?.cancel();
    _widgetCache.clear();
    super.dispose();
  }

  void _onScroll() {
    // Дебаунсинг для оптимизации
    _scrollDebouncer?.cancel();
    _scrollDebouncer = Timer(const Duration(milliseconds: 100), () {
      final currentState = _dataStateNotifier.value;
      if (_scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent -
                  _BalanceScreenConstants.preloadThreshold &&
          currentState.hasMoreData &&
          !currentState.isLoadingMore &&
          !_isRequestInProgress) {
        _loadMoreData();
      }
    });
  }

  Future<void> _loadInitialData() async {
    if (!mounted || _isRequestInProgress) return;

    _isRequestInProgress = true;
    _updateState(
      _dataStateNotifier.value.copyWith(loadingState: LoadingState.loading),
    );

    try {
      final result = await UserRepository.getMoneyHistory(
        GetMoneyHistoryInput(
          pagination: Pagination(
            page: 1,
            perPage: _BalanceScreenConstants.itemsPerPage,
          ),
        ),
      );

      if (!mounted) return;

      final data = result.data;
      if (data != null) {
        final items = data.items ?? [];
        final newState = _dataStateNotifier.value
            .copyWith(
              loadingState:
                  items.isEmpty ? LoadingState.empty : LoadingState.loaded,
              historyItems: items,
              hasMoreData: items.length >= _BalanceScreenConstants.itemsPerPage,
              currentPage: 1,
            )
            .withCachedPage(0, items);

        _updateState(newState);
      } else {
        _updateState(
          _dataStateNotifier.value.copyWith(
            loadingState: LoadingState.error,
            errorMessage: _BalanceScreenConstants.errorText,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      _updateState(
        _dataStateNotifier.value.copyWith(
          loadingState: LoadingState.error,
          errorMessage: e.toString(),
        ),
      );
    } finally {
      _isRequestInProgress = false;
    }

    // Также обновляем данные пользователя
    _refreshUserData();
  }

  Future<void> _loadMoreData() async {
    if (!mounted || _isRequestInProgress) return;

    final currentState = _dataStateNotifier.value;
    if (!currentState.hasMoreData || currentState.isLoadingMore) return;

    _isRequestInProgress = true;
    _updateState(currentState.copyWith(loadingState: LoadingState.loadingMore));

    try {
      final nextPage = currentState.currentPage + 1;

      // Проверяем кэш
      final cachedItems = currentState.getCachedPage(nextPage);
      if (cachedItems != null) {
        final allItems = [...currentState.historyItems, ...cachedItems];
        _updateState(
          currentState.copyWith(
            loadingState: LoadingState.loaded,
            historyItems: allItems,
            hasMoreData:
                cachedItems.length >= _BalanceScreenConstants.itemsPerPage,
            currentPage: nextPage,
          ),
        );
        return;
      }

      final result = await UserRepository.getMoneyHistory(
        GetMoneyHistoryInput(
          pagination: Pagination(
            page: nextPage,
            perPage: _BalanceScreenConstants.itemsPerPage,
          ),
        ),
      );

      if (!mounted) return;

      final data = result.data;
      if (data != null) {
        final newItems = data.items ?? [];
        final allItems = [...currentState.historyItems, ...newItems];

        final newState = currentState
            .copyWith(
              loadingState: LoadingState.loaded,
              historyItems: allItems,
              hasMoreData:
                  newItems.length >= _BalanceScreenConstants.itemsPerPage,
              currentPage: nextPage,
            )
            .withCachedPage(nextPage, newItems);

        _updateState(newState);
      } else {
        _updateState(currentState.copyWith(loadingState: LoadingState.loaded));
      }
    } catch (e) {
      if (!mounted) return;
      _updateState(currentState.copyWith(loadingState: LoadingState.loaded));
    } finally {
      _isRequestInProgress = false;
    }
  }

  Future<void> _refreshUserData() async {
    try {
      final result = await UserRepository.getSelf();
      final user = result.data?.user;

      if (user != null && mounted) {
        context.read<BlocUser>().add(SetSelf(user));
      } else if (user == null && mounted) {
        context.router.replace(AuthorizationRoute());
      }
    } catch (e) {
      // Игнорируем ошибки обновления пользователя
    }
  }

  Future<void> _onRefresh() async {
    _widgetCache.clear(); // Очищаем кэш виджетов при обновлении
    MemoizedMoneyHistoryCard.clearCache(); // Очищаем кэш карточек
    await _loadInitialData();
  }

  void _updateState(_BalanceDataState newState) {
    if (mounted) {
      _dataStateNotifier.value = newState;
    }
  }

  // Мемоизированные методы построения UI
  Widget _buildHeader(BuildContext context, BlocUserState userState) {
    final cacheKey =
        'header_${userState.self.money?.coins}_${userState.self.money?.gems?.first.value}';

    return _widgetCache[cacheKey] ??= RepaintBoundary(
      child: Padding(
        padding: const EdgeInsets.all(UIConstants.paddingLarge),
        child: Row(
          children: [
            _OptimizedUserWidget(
              user: userState.self,
              onTap: () => context.router.push(UserRoute()),
            ),
            // const SizedBox(width: UIConstants.paddingLarge),
            const Spacer(),
            _buildBalance(context, userState),
            const SizedBox(width: UIConstants.paddingSmall),
          ],
        ),
      ),
    );
  }

  Widget _buildBalance(BuildContext context, BlocUserState userState) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final coinColor = isDarkTheme ? AppColors.darkCoin : AppColors.lightCoin;
    final gemColor = isDarkTheme ? AppColors.darkGem : AppColors.lightGem;
    final gems = BlocUser.getCompressionGem(userState.self);

    return _OptimizedBalanceWidget(
      coins: userState.self.money?.coins ?? 0,
      gems: gems.value ?? 0,
      coinColor: coinColor,
      gemColor: gemColor,
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    const cacheKey = 'action_buttons';

    return _widgetCache[cacheKey] ??= RepaintBoundary(
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: UIConstants.paddingMedium,
        ),
        child: Row(
          children: [
            Expanded(
              child: _OptimizedActionButton(
                text: _BalanceScreenConstants.scanQrText,
                icon: CupertinoIcons.qrcode,
                onTap: () {
                  context.router.push(ScanRoute()).then((_) {
                    _widgetCache.clear();
                    _loadInitialData();
                  });
                },
              ),
            ),
            const SizedBox(width: _BalanceScreenConstants.actionButtonSpacing),
            Expanded(
              child: _OptimizedActionButton(
                text: _BalanceScreenConstants.addCheckText,
                icon: CupertinoIcons.add,
                onTap: () => context.router.push(AddCheckRoute()),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryList(_BalanceDataState dataState) {
    switch (dataState.loadingState) {
      case LoadingState.loading:
        return const SliverFillRemaining(
          child: Center(child: CupertinoActivityIndicator()),
        );

      case LoadingState.error:
        return SliverFillRemaining(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  dataState.errorMessage ?? _BalanceScreenConstants.errorText,
                  style: Fonts.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: UIConstants.paddingLarge),
                CupertinoButton(
                  onPressed: _loadInitialData,
                  child: const Text(_BalanceScreenConstants.retryText),
                ),
              ],
            ),
          ),
        );

      case LoadingState.empty:
        return const SliverFillRemaining(
          child: Center(
            child: Text(
              _BalanceScreenConstants.emptyHistoryText,
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ),
        );

      case LoadingState.loaded:
      case LoadingState.loadingMore:
        return SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              if (index < dataState.historyItems.length) {
                return RepaintBoundary(
                  child: Padding(
                    padding: const EdgeInsets.only(
                      left: UIConstants.paddingMedium,
                      right: UIConstants.paddingMedium,
                      bottom: _BalanceScreenConstants.historyItemSpacing,
                    ),
                    child: MemoizedMoneyHistoryCard(
                      data: dataState.historyItems[index],
                    ),
                  ),
                );
              } else if (dataState.isLoadingMore) {
                return const Padding(
                  padding: EdgeInsets.all(UIConstants.paddingLarge),
                  child: Center(child: CupertinoActivityIndicator()),
                );
              } else if (!dataState.hasMoreData &&
                  dataState.historyItems.isNotEmpty) {
                return Padding(
                  padding: const EdgeInsets.all(UIConstants.paddingLarge),
                  child: Center(
                    child: Text(
                      _BalanceScreenConstants.noMoreDataText,
                      style: Fonts.bodySmall.copyWith(
                        color: CupertinoColors.secondaryLabel.resolveFrom(
                          context,
                        ),
                      ),
                    ),
                  ),
                );
              }
              return null;
            },
            childCount:
                dataState.historyItems.length +
                (dataState.isLoadingMore ? 1 : 0) +
                (!dataState.hasMoreData && dataState.historyItems.isNotEmpty
                    ? 1
                    : 0),
          ),
        );

      case LoadingState.initial:
        return const SliverToBoxAdapter(child: SizedBox.shrink());
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Для AutomaticKeepAliveClientMixin

    return BlocBuilder<BlocUser, BlocUserState>(
      builder: (context, userState) {
        final isAdmin = userState.self.role == UserRole.admin;

        return Scaffold(
          floatingActionButton:
              isAdmin
                  ? FloatingActionButton(
                    elevation: 0.0,
                    onPressed: () => context.router.push(AdminRoute()),
                    shape: const CircleBorder(),
                    child: const Icon(CupertinoIcons.lock_shield),
                  )
                  : null,
          appBar: CupertinoNavigationBar(
            backgroundColor: CupertinoColors.systemBackground.resolveFrom(
              context,
            ),
            border: Border(
              bottom: BorderSide(
                color: CupertinoColors.separator.resolveFrom(context),
                width: 0.5,
              ),
            ),
            previousPageTitle: '',
            middle: const Text(
              _BalanceScreenConstants.screenTitle,
              style: TextStyle(fontSize: 17.0, fontWeight: FontWeight.w600),
            ),
          ),
          body: RefreshIndicator(
            onRefresh: _onRefresh,
            child: ValueListenableBuilder<_BalanceDataState>(
              valueListenable: _dataStateNotifier,
              builder: (context, dataState, child) {
                return CustomScrollView(
                  controller: _scrollController,
                  physics: const AlwaysScrollableScrollPhysics(
                    parent: BouncingScrollPhysics(),
                  ),
                  slivers: [
                    // Header with user info and balance
                    SliverToBoxAdapter(child: _buildHeader(context, userState)),

                    // Action buttons
                    SliverToBoxAdapter(child: _buildActionButtons(context)),

                    const SliverToBoxAdapter(
                      child: SizedBox(
                        height: _BalanceScreenConstants.headerSpacing,
                      ),
                    ),

                    // History list
                    _buildHistoryList(dataState),

                    // Bottom padding
                    SliverToBoxAdapter(
                      child: SizedBox(
                        height:
                            isAdmin
                                ? _BalanceScreenConstants.adminBottomPadding
                                : _BalanceScreenConstants.bottomPadding,
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        );
      },
    );
  }
}
