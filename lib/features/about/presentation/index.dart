import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tony_clou_app/core/gen/assets.gen.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/image/index.dart';

@RoutePage()
class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CupertinoNavigationBar(
        backgroundColor: CupertinoColors.systemBackground.resolveFrom(context),
        border: Border(
          bottom: BorderSide(
            color: CupertinoColors.separator.resolveFrom(context),
            width: 0.5,
          ),
        ),
        previousPageTitle: '',
        middle: const Text(
          'О нас',
          style: TextStyle(fontSize: 17.0, fontWeight: FontWeight.w600),
        ),
      ),
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Company logo/header section
            Container(
              padding: EdgeInsets.all(24.0),
              decoration: BoxDecoration(
                color: CupertinoColors.systemBackground.resolveFrom(context),
                borderRadius: BorderRadius.circular(12.0),
                border: Border.all(
                  color: CupertinoColors.separator.resolveFrom(context),
                  width: 0.5,
                ),
              ),
              child: Column(
                children: [
                  Container(
                    width: 120.0,
                    height: 120.0,
                    clipBehavior: Clip.hardEdge,
                    decoration: BoxDecoration(
                      color: AppColors.lightSecondary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20.0),
                    ),
                    child: Assets.images.logo.image(),
                  ),
                  SizedBox(height: 16.0),
                  Text(
                    'Tony Clou',
                    style: Fonts.headlineMedium.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8.0),
                  Text(
                    'Дизайнерская одежда',
                    style: Fonts.bodyMedium.copyWith(
                      color: CupertinoColors.secondaryLabel.resolveFrom(
                        context,
                      ),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            SizedBox(height: 20.0),

            // About section
            _buildSection(
              context,
              'О компании',
              'Tony Clou это дизайнерская одежда со своим почерком и стилем. Основная концепция это создание стрит-стайл образов в лимитированной версии.',
              CupertinoIcons.info_circle,
            ),

            SizedBox(height: 16.0),
            _buildImage(context, Assets.images.image2.image()),
            SizedBox(height: 16.0),

            // Mission section
            _buildSection(
              context,
              'Наша миссия',
              'Идея бренда строится на индивидуальности и неповторимости образов, в которых каждый человек сможет подчеркнуть свою личность.',
              CupertinoIcons.scope,
            ),

            SizedBox(height: 16.0),
            _buildImage(context, Assets.images.image1.image()),
            SizedBox(height: 16.0),

            // Mission section
            _buildSection(
              context,
              'Пару слов подробнее',
              'Основная идея дизайнера TONY CLOU заключается в том, чтобы создавать оригинальные образы, неповторимые модели и детали гардероба, которые выделят вашу индивидуальность, независимо от того, являетесь ли вы представителем творческой профессии или офисным сотрудником.',
              CupertinoIcons.scope,
            ),

            SizedBox(height: 16.0),
            _buildImage(context, Assets.images.image3.image()),
            SizedBox(height: 16.0),

            // Values section
            _buildSection(
              context,
              'Наши ценности',
              '– Индивидуальность\n'
                  '– Оригинальность\n'
                  '– Стиль\n'
                  '– Открытость и честность\n'
                  '– Ответственность',
              CupertinoIcons.heart,
            ),

            SizedBox(height: 16.0),
            Text(
              'Tony Clou',
              style: Fonts.titleSmall,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.0),
          ],
        ),
      ),
    );
  }

  Widget _buildImage(BuildContext context, Widget image) {
    return Center(
      child: GestureDetector(
        onTap: () => ImagePreviews.showImagePreview(context, image),
        child: AnimatedSize(
          duration: Duration(milliseconds: 300),
          child: Container(
            clipBehavior: Clip.hardEdge,
            // padding: EdgeInsets.all(16.0),
            constraints: BoxConstraints(minHeight: 100.0, minWidth: 100.0),
            decoration: BoxDecoration(
              color: CupertinoColors.systemBackground.resolveFrom(context),
              borderRadius: BorderRadius.circular(12.0),
              border: Border.all(
                color: CupertinoColors.separator.resolveFrom(context),
                width: 0.5,
              ),
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [CupertinoActivityIndicator(), image],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSection(
    BuildContext context,
    String title,
    String content,
    IconData icon,
  ) {
    return Container(
      padding: EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: CupertinoColors.systemBackground.resolveFrom(context),
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(
          color: CupertinoColors.separator.resolveFrom(context),
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  color: AppColors.lightSecondary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Icon(icon, color: AppColors.lightSecondary, size: 20.0),
              ),
              SizedBox(width: 12.0),
              Text(
                title,
                style: Fonts.labelLarge.copyWith(fontWeight: FontWeight.w600),
              ),
            ],
          ),
          SizedBox(height: 12.0),
          Text(
            content,
            style: Fonts.bodyMedium.copyWith(
              color: CupertinoColors.label.resolveFrom(context),
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  // Widget _buildStatItem(BuildContext context, String number, String label) {
  //   return Column(
  //     children: [
  //       Text(
  //         number,
  //         style: Fonts.headlineSmall.copyWith(
  //           fontWeight: FontWeight.bold,
  //           color: CupertinoColors.systemGreen,
  //         ),
  //       ),
  //       SizedBox(height: 4.0),
  //       Text(
  //         label,
  //         style: Fonts.bodySmall.copyWith(
  //           color: CupertinoColors.secondaryLabel.resolveFrom(context),
  //         ),
  //         textAlign: TextAlign.center,
  //       ),
  //     ],
  //   );
  // }
}
