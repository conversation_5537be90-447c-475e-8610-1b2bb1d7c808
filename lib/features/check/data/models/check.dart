import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';

part 'check.freezed.dart';
part 'check.g.dart';

@freezed
abstract class Check with _$Check {
  @JsonSerializable(includeIfNull: false)
  factory Check({
    String? id,
    String? contentType,
    DateTime? timestamp,
    String? userId,
    User? user,
  }) = _Check;

  factory Check.fromJson(Map<String, dynamic> json) => _$CheckFromJson(json);
}
