// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Check _$CheckFromJson(Map<String, dynamic> json) => _Check(
  id: json['id'] as String?,
  contentType: json['contentType'] as String?,
  timestamp:
      json['timestamp'] == null
          ? null
          : DateTime.parse(json['timestamp'] as String),
  userId: json['userId'] as String?,
  user:
      json['user'] == null
          ? null
          : User.fromJson(json['user'] as Map<String, dynamic>),
);

Map<String, dynamic> _$CheckToJson(_Check instance) => <String, dynamic>{
  if (instance.id case final value?) 'id': value,
  if (instance.contentType case final value?) 'contentType': value,
  if (instance.timestamp?.toIso8601String() case final value?)
    'timestamp': value,
  if (instance.userId case final value?) 'userId': value,
  if (instance.user case final value?) 'user': value,
};
