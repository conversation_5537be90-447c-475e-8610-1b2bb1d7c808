// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'index.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_GetChecksOutput _$GetChecksOutputFromJson(Map<String, dynamic> json) =>
    _GetChecksOutput(
      items:
          (json['items'] as List<dynamic>?)
              ?.map((e) => Check.fromJson(e as Map<String, dynamic>))
              .toList(),
      totalItems: (json['totalItems'] as num?)?.toInt(),
    );

Map<String, dynamic> _$GetChecksOutputToJson(_GetChecksOutput instance) =>
    <String, dynamic>{
      if (instance.items case final value?) 'items': value,
      if (instance.totalItems case final value?) 'totalItems': value,
    };

_GetChecksInput _$GetChecksInputFromJson(Map<String, dynamic> json) =>
    _GetChecksInput(
      query: json['query'] as String?,
      pagination:
          json['pagination'] == null
              ? null
              : Pagination.fromJson(json['pagination'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$GetChecksInputToJson(_GetChecksInput instance) =>
    <String, dynamic>{
      if (instance.query case final value?) 'query': value,
      if (instance.pagination case final value?) 'pagination': value,
    };
