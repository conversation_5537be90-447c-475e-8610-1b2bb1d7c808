// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'index.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GetChecksOutput {

 List<Check>? get items; int? get totalItems;
/// Create a copy of GetChecksOutput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GetChecksOutputCopyWith<GetChecksOutput> get copyWith => _$GetChecksOutputCopyWithImpl<GetChecksOutput>(this as GetChecksOutput, _$identity);

  /// Serializes this GetChecksOutput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GetChecksOutput&&const DeepCollectionEquality().equals(other.items, items)&&(identical(other.totalItems, totalItems) || other.totalItems == totalItems));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(items),totalItems);

@override
String toString() {
  return 'GetChecksOutput(items: $items, totalItems: $totalItems)';
}


}

/// @nodoc
abstract mixin class $GetChecksOutputCopyWith<$Res>  {
  factory $GetChecksOutputCopyWith(GetChecksOutput value, $Res Function(GetChecksOutput) _then) = _$GetChecksOutputCopyWithImpl;
@useResult
$Res call({
 List<Check>? items, int? totalItems
});




}
/// @nodoc
class _$GetChecksOutputCopyWithImpl<$Res>
    implements $GetChecksOutputCopyWith<$Res> {
  _$GetChecksOutputCopyWithImpl(this._self, this._then);

  final GetChecksOutput _self;
  final $Res Function(GetChecksOutput) _then;

/// Create a copy of GetChecksOutput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? items = freezed,Object? totalItems = freezed,}) {
  return _then(_self.copyWith(
items: freezed == items ? _self.items : items // ignore: cast_nullable_to_non_nullable
as List<Check>?,totalItems: freezed == totalItems ? _self.totalItems : totalItems // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc

@JsonSerializable(includeIfNull: false)
class _GetChecksOutput implements GetChecksOutput {
   _GetChecksOutput({final  List<Check>? items, this.totalItems}): _items = items;
  factory _GetChecksOutput.fromJson(Map<String, dynamic> json) => _$GetChecksOutputFromJson(json);

 final  List<Check>? _items;
@override List<Check>? get items {
  final value = _items;
  if (value == null) return null;
  if (_items is EqualUnmodifiableListView) return _items;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  int? totalItems;

/// Create a copy of GetChecksOutput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GetChecksOutputCopyWith<_GetChecksOutput> get copyWith => __$GetChecksOutputCopyWithImpl<_GetChecksOutput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GetChecksOutputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetChecksOutput&&const DeepCollectionEquality().equals(other._items, _items)&&(identical(other.totalItems, totalItems) || other.totalItems == totalItems));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_items),totalItems);

@override
String toString() {
  return 'GetChecksOutput(items: $items, totalItems: $totalItems)';
}


}

/// @nodoc
abstract mixin class _$GetChecksOutputCopyWith<$Res> implements $GetChecksOutputCopyWith<$Res> {
  factory _$GetChecksOutputCopyWith(_GetChecksOutput value, $Res Function(_GetChecksOutput) _then) = __$GetChecksOutputCopyWithImpl;
@override @useResult
$Res call({
 List<Check>? items, int? totalItems
});




}
/// @nodoc
class __$GetChecksOutputCopyWithImpl<$Res>
    implements _$GetChecksOutputCopyWith<$Res> {
  __$GetChecksOutputCopyWithImpl(this._self, this._then);

  final _GetChecksOutput _self;
  final $Res Function(_GetChecksOutput) _then;

/// Create a copy of GetChecksOutput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? items = freezed,Object? totalItems = freezed,}) {
  return _then(_GetChecksOutput(
items: freezed == items ? _self._items : items // ignore: cast_nullable_to_non_nullable
as List<Check>?,totalItems: freezed == totalItems ? _self.totalItems : totalItems // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}


/// @nodoc
mixin _$GetChecksInput {

 String? get query; Pagination? get pagination;
/// Create a copy of GetChecksInput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GetChecksInputCopyWith<GetChecksInput> get copyWith => _$GetChecksInputCopyWithImpl<GetChecksInput>(this as GetChecksInput, _$identity);

  /// Serializes this GetChecksInput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GetChecksInput&&(identical(other.query, query) || other.query == query)&&(identical(other.pagination, pagination) || other.pagination == pagination));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,query,pagination);

@override
String toString() {
  return 'GetChecksInput(query: $query, pagination: $pagination)';
}


}

/// @nodoc
abstract mixin class $GetChecksInputCopyWith<$Res>  {
  factory $GetChecksInputCopyWith(GetChecksInput value, $Res Function(GetChecksInput) _then) = _$GetChecksInputCopyWithImpl;
@useResult
$Res call({
 String? query, Pagination? pagination
});


$PaginationCopyWith<$Res>? get pagination;

}
/// @nodoc
class _$GetChecksInputCopyWithImpl<$Res>
    implements $GetChecksInputCopyWith<$Res> {
  _$GetChecksInputCopyWithImpl(this._self, this._then);

  final GetChecksInput _self;
  final $Res Function(GetChecksInput) _then;

/// Create a copy of GetChecksInput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? query = freezed,Object? pagination = freezed,}) {
  return _then(_self.copyWith(
query: freezed == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String?,pagination: freezed == pagination ? _self.pagination : pagination // ignore: cast_nullable_to_non_nullable
as Pagination?,
  ));
}
/// Create a copy of GetChecksInput
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaginationCopyWith<$Res>? get pagination {
    if (_self.pagination == null) {
    return null;
  }

  return $PaginationCopyWith<$Res>(_self.pagination!, (value) {
    return _then(_self.copyWith(pagination: value));
  });
}
}


/// @nodoc

@JsonSerializable(includeIfNull: false)
class _GetChecksInput implements GetChecksInput {
   _GetChecksInput({this.query, this.pagination});
  factory _GetChecksInput.fromJson(Map<String, dynamic> json) => _$GetChecksInputFromJson(json);

@override final  String? query;
@override final  Pagination? pagination;

/// Create a copy of GetChecksInput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GetChecksInputCopyWith<_GetChecksInput> get copyWith => __$GetChecksInputCopyWithImpl<_GetChecksInput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GetChecksInputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetChecksInput&&(identical(other.query, query) || other.query == query)&&(identical(other.pagination, pagination) || other.pagination == pagination));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,query,pagination);

@override
String toString() {
  return 'GetChecksInput(query: $query, pagination: $pagination)';
}


}

/// @nodoc
abstract mixin class _$GetChecksInputCopyWith<$Res> implements $GetChecksInputCopyWith<$Res> {
  factory _$GetChecksInputCopyWith(_GetChecksInput value, $Res Function(_GetChecksInput) _then) = __$GetChecksInputCopyWithImpl;
@override @useResult
$Res call({
 String? query, Pagination? pagination
});


@override $PaginationCopyWith<$Res>? get pagination;

}
/// @nodoc
class __$GetChecksInputCopyWithImpl<$Res>
    implements _$GetChecksInputCopyWith<$Res> {
  __$GetChecksInputCopyWithImpl(this._self, this._then);

  final _GetChecksInput _self;
  final $Res Function(_GetChecksInput) _then;

/// Create a copy of GetChecksInput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? query = freezed,Object? pagination = freezed,}) {
  return _then(_GetChecksInput(
query: freezed == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String?,pagination: freezed == pagination ? _self.pagination : pagination // ignore: cast_nullable_to_non_nullable
as Pagination?,
  ));
}

/// Create a copy of GetChecksInput
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaginationCopyWith<$Res>? get pagination {
    if (_self.pagination == null) {
    return null;
  }

  return $PaginationCopyWith<$Res>(_self.pagination!, (value) {
    return _then(_self.copyWith(pagination: value));
  });
}
}

// dart format on
