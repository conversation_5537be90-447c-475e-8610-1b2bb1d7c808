import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:tony_clou_app/features/check/data/models/check.dart';
import 'package:tony_clou_app/shared/data/models/pagination.dart';

part 'index.freezed.dart';
part 'index.g.dart';

@freezed
abstract class GetChecksOutput with _$GetChecksOutput {
  @JsonSerializable(includeIfNull: false)
  factory GetChecksOutput({List<Check>? items, int? totalItems}) =
      _GetChecksOutput;

  factory GetChecksOutput.fromJson(Map<String, dynamic> json) =>
      _$GetChecksOutputFromJson(json);
}

@freezed
abstract class GetChecksInput with _$GetChecksInput {
  @JsonSerializable(includeIfNull: false)
  factory GetChecksInput({String? query, Pagination? pagination}) =
      _GetChecksInput;

  factory GetChecksInput.fromJson(Map<String, dynamic> json) =>
      _$GetChecksInputFromJson(json);
}
