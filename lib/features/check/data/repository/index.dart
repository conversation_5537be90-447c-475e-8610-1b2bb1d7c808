import 'dart:io';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:tony_clou_app/features/check/data/models/index.dart';
import 'package:tony_clou_app/shared/data/api.dart';

class CheckRepository {
  static Future<Response<GetChecksOutput>> getChecks(
    GetChecksInput data,
  ) async {
    final body = data.toJson();

    final request = await API.request<GetChecksOutput>(
      url: '/checks/getChecks',
      body: body,
      fromJson: GetChecksOutput.fromJson,
      method: 'POST',
    );

    return request;
  }

  static Future<Response<dynamic>> createCheck(File image) async {
    // Validate file existence
    if (!await image.exists()) {
      throw Exception('Image file does not exist');
    }

    // Determine MIME type based on file extension
    final extension = image.path.split('.').last.toLowerCase();
    String mimeType;
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        mimeType = 'image/jpeg';
        break;
      case 'png':
        mimeType = 'image/png';
        break;
      case 'gif':
        mimeType = 'image/gif';
        break;
      default:
        throw Exception('Invalid image type. Only JPEG, PNG, or GIF allowed.');
    }

    final body = FormData.fromMap({
      'image': await MultipartFile.fromFile(
        image.path,
        filename: image.path.split('/').last,
        contentType: DioMediaType.parse(mimeType),
      ),
    });

    final request = await API.request<dynamic>(
      url: '/checks/createCheck',
      body: body,
      // fromJson: CreateCheckOutput.fromJson,
      method: 'POST',
      options: Options(contentType: 'multipart/form-data'),
    );

    return request;
  }

  static Future<Response<Uint8List>> getCheckImage(String id) async {
    try {
      final request = await API.fetchImage(
        imageUrl: '/checks/getCheckImage/$id',
      );

      return request;
    } catch (e) {
      print('Error fetching check image: $e');
      rethrow;
    }
  }
}
