import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:camera/camera.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tony_clou_app/features/check/data/repository/index.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/wrapper/index.dart';
import 'package:tony_clou_app/shared/widgets/interactive/elevated_button.dart';
import 'package:tony_clou_app/shared/widgets/utility/snackbar.dart';

@RoutePage()
class AddCheckScreen extends StatefulWidget {
  const AddCheckScreen({super.key});

  @override
  State<AddCheckScreen> createState() => _AddCheckScreenState();
}

class _AddCheckScreenState extends State<AddCheckScreen> {
  bool _isLoading = false;
  CameraController? _controller;
  Future<void>? _initializeControllerFuture;
  XFile? _image;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  Future<void> _initializeCamera() async {
    try {
      final cameras = await availableCameras();
      final firstCamera = cameras.first;
      _controller = CameraController(
        firstCamera,
        ResolutionPreset.high,
        enableAudio: false,
      );
      _initializeControllerFuture = _controller!.initialize();
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        CustomSnackbar(
          text: 'Ошибка инициализации камеры: $e',
        ).toSnackBar(context),
      );
    }
  }

  Future<void> _takePhoto() async {
    try {
      await _initializeControllerFuture;
      final image = await _controller!.takePicture();
      setState(() {
        _image = image;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        CustomSnackbar(text: 'Ошибка при съемке: $e').toSnackBar(context),
      );
    }
  }

  void _cancel() {
    setState(() {
      _image = null;
    });
  }

  Future<void> _continue() async {
    if (_image == null) return;

    try {
      setState(() {
        _isLoading = true;
      });

      // Call createCheck from CheckRepository
      final response = await CheckRepository.createCheck(File(_image!.path));

      if (response.statusCode == 200) {
        // Success: Show confirmation and navigate back
        ScaffoldMessenger.of(context).showSnackBar(
          const CustomSnackbar(
            text: 'Чек успешно отправлен',
          ).toSnackBar(context),
        );
        context.router.pop();
      } else {
        // Handle non-200 responses
        ScaffoldMessenger.of(context).showSnackBar(
          CustomSnackbar(
            text: 'Ошибка: ${response.statusMessage}',
          ).toSnackBar(context),
        );
      }
    } catch (e) {
      // Handle errors (e.g., network issues, invalid image type)
      ScaffoldMessenger.of(context).showSnackBar(
        CustomSnackbar(
          text: 'Ошибка при отправке чека: $e',
        ).toSnackBar(context),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Wrapper(
      appBar: CupertinoNavigationBar(
        automaticallyImplyLeading: true,
        previousPageTitle: 'Баланс',
        middle: Text(
          _isLoading ? 'Loading...' : 'Добавление чека',
          style: Fonts.labelMedium,
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: Center(
              child:
                  _image == null
                      ? FutureBuilder<void>(
                        future: _initializeControllerFuture,
                        builder: (context, snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.done) {
                            if (_controller != null) {
                              return AspectRatio(
                                aspectRatio: 9 / 16,
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(10.0),
                                  child: CameraPreview(_controller!),
                                ),
                              );
                            } else {
                              return const Text('Камера недоступна');
                            }
                          } else {
                            return const Center(
                              child: CupertinoActivityIndicator(),
                            );
                          }
                        },
                      )
                      : AspectRatio(
                        aspectRatio: 9 / 16,
                        child: Image.file(
                          File(_image!.path),
                          fit: BoxFit.cover,
                        ),
                      ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              spacing: 8.0,
              children: [
                if (_image == null)
                  SizedBox(
                    width: 56,
                    height: 56,
                    child: IconButton.filled(
                      onPressed: _takePhoto,
                      icon: Icon(CupertinoIcons.camera),
                    ),
                  ),
                if (_image != null) ...[
                  Expanded(
                    child: CustomElevatedButton(
                      onPressed: _cancel,
                      text: 'Отмена',
                    ),
                  ),
                  Expanded(
                    child: CustomElevatedButton(
                      onPressed: _continue,
                      type: CustomElevatedButtonTypes.accent,
                      text: _isLoading ? null : 'Готово',
                      child: _isLoading ? CupertinoActivityIndicator() : null,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
