import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:tony_clou_app/core/navigation/index.gr.dart';
import 'package:tony_clou_app/features/check/data/models/check.dart';
import 'package:tony_clou_app/features/check/data/repository/index.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/card/index.dart';
import 'package:tony_clou_app/shared/widgets/containers/image/index.dart';

class CheckCard extends StatelessWidget {
  const CheckCard({super.key, required this.data});

  final Check data;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    final descriptionTextStyle = Fonts.bodySmall.merge(
      TextStyle(
        color:
            isDarkTheme
                ? AppColors.darkDescription
                : AppColors.lightDescription,
      ),
    );

    return CustomCard(
      onTap: () {
        context.router.push(UserRoute(userId: data.userId));
      },
      child: Row(
        children: [
          if (data.id != null)
            ImageFromRequest(
              onTap:
                  () => ImagePreviews.showImagePreviewWithRequest(
                    context,
                    () => CheckRepository.getCheckImage(data.id!),
                  ),
              width: 96,
              height: 96,
              borderRadius: 12.0,
              request: () => CheckRepository.getCheckImage(data.id!),
            ),
          SizedBox(width: 12.0),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${data.user?.surname} ${data.user?.name}',
                  style: Fonts.labelSmall,
                ),
                SizedBox(height: 8.0),
                Text(
                  '${data.user?.telephoneNumber}',
                  style: descriptionTextStyle,
                ),
                Text('${data.user?.email}', style: descriptionTextStyle),
                if (data.timestamp != null)
                  Text(DateFormat("dd.MM.yyyy, HH:mm").format(data.timestamp!)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
