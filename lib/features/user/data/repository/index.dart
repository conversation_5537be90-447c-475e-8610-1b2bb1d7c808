import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:tony_clou_app/features/authorization/data/models/index.dart';
import 'package:tony_clou_app/features/user/data/models/index.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';
import 'package:tony_clou_app/shared/data/api.dart';

class UserRepository {
  static Future<Response<GetDevicesOutput>> getDevices() async {
    final body = {};

    final request = await API.request<GetDevicesOutput>(
      url: '/auth/getDevices',
      body: json<PERSON>ncode(body),
      fromJson: GetDevicesOutput.fromJson,
      method: 'POST',
    );

    return request;
  }

  static Future<Response<GetUserOutput>> getSelf() async {
    final body = {};

    final request = await API.request<GetUserOutput>(
      url: '/users/getSelf',
      body: json<PERSON>nco<PERSON>(body),
      fromJson: GetUserOutput.fromJson,
      method: 'POST',
    );
    final userId = request.data?.user?.id;
    if (userId != null && !kIsWeb) {
      await OneSignal.login(userId);
      print('OneSignal user ID установлен: $userId');
    }

    return request;
  }

  static Future<Response<GetUserOutput>> getUser(String id) async {
    final body = jsonEncode({'id': id});

    final request = await API.request<GetUserOutput>(
      url: '/users/getUser',
      body: body,
      fromJson: GetUserOutput.fromJson,
      method: 'POST',
    );

    return request;
  }

  static Future<Response<EmailVerifyCodeOutput>> create(
    User user, {
    bool self = true,
  }) async {
    final body = user.toJson();

    final request = await API.request<EmailVerifyCodeOutput>(
      url: '/users/createUser',
      body: body,
      fromJson: EmailVerifyCodeOutput.fromJson,
      method: 'POST',
    );

    final userId = request.data?.user?.id;
    if (userId != null && self && !kIsWeb) {
      await OneSignal.login(userId);
      print('OneSignal user ID установлен: $userId');
    }

    return request;
  }

  static Future<Response<EmailVerifyCodeOutput>> edit(User user) async {
    final body = user.toJson();

    final request = await API.request<EmailVerifyCodeOutput>(
      url: '/users/editUser',
      body: body,
      fromJson: EmailVerifyCodeOutput.fromJson,
      method: 'POST',
    );

    return request;
  }

  static Future<Response<GetUsersOutput>> getUsers(GetUsersInput data) async {
    final body = data.toJson();

    final request = await API.request<GetUsersOutput>(
      url: '/users/getUsers',
      body: body,
      fromJson: GetUsersOutput.fromJson,
      method: 'POST',
    );

    return request;
  }

  static Future<Response<GetMoneyHistoryOutput>> getMoneyHistory(
    GetMoneyHistoryInput data,
  ) async {
    final body = data.toJson();

    final request = await API.request<GetMoneyHistoryOutput>(
      url: '/money/getHistory',
      body: body,
      fromJson: GetMoneyHistoryOutput.fromJson,
      method: 'POST',
    );

    return request;
  }
}
