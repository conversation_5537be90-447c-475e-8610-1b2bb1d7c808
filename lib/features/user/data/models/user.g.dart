// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_User _$UserFromJson(Map<String, dynamic> json) => _User(
  id: json['id'] as String?,
  email: json['email'] as String?,
  role: $enumDecodeNullable(_$UserRoleEnumMap, json['role']),
  name: json['name'] as String?,
  surname: json['surname'] as String?,
  money:
      json['money'] == null
          ? null
          : UserMoney.fromJson(json['money'] as Map<String, dynamic>),
  dateOfBirth:
      json['dateOfBirth'] == null
          ? null
          : DateTime.parse(json['dateOfBirth'] as String),
  telephoneNumber: json['telephoneNumber'] as String?,
  isBlocked: json['isBlocked'] as bool?,
  createdAt:
      json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
  updatedAt:
      json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$UserToJson(_User instance) => <String, dynamic>{
  if (instance.id case final value?) 'id': value,
  if (instance.email case final value?) 'email': value,
  if (_$UserRoleEnumMap[instance.role] case final value?) 'role': value,
  if (instance.name case final value?) 'name': value,
  if (instance.surname case final value?) 'surname': value,
  if (instance.money case final value?) 'money': value,
  if (instance.dateOfBirth?.toIso8601String() case final value?)
    'dateOfBirth': value,
  if (instance.telephoneNumber case final value?) 'telephoneNumber': value,
  if (instance.isBlocked case final value?) 'isBlocked': value,
  if (instance.createdAt?.toIso8601String() case final value?)
    'createdAt': value,
  if (instance.updatedAt?.toIso8601String() case final value?)
    'updatedAt': value,
};

const _$UserRoleEnumMap = {UserRole.admin: 'admin', UserRole.user: 'user'};

_UserFilter _$UserFilterFromJson(Map<String, dynamic> json) => _UserFilter(
  id: json['id'] as String?,
  email: json['email'] as String?,
  role: $enumDecodeNullable(_$UserRoleEnumMap, json['role']),
  name: json['name'] as String?,
  surname: json['surname'] as String?,
  money:
      json['money'] == null
          ? null
          : UserMoney.fromJson(json['money'] as Map<String, dynamic>),
  dateOfBirth:
      json['dateOfBirth'] == null
          ? null
          : DateTime.parse(json['dateOfBirth'] as String),
  telephoneNumber: json['telephoneNumber'] as String?,
  isBlocked: json['isBlocked'] as bool?,
  createdAt:
      json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
  updatedAt:
      json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
  query: json['query'] as String?,
);

Map<String, dynamic> _$UserFilterToJson(_UserFilter instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'role': _$UserRoleEnumMap[instance.role],
      'name': instance.name,
      'surname': instance.surname,
      'money': instance.money,
      'dateOfBirth': instance.dateOfBirth?.toIso8601String(),
      'telephoneNumber': instance.telephoneNumber,
      'isBlocked': instance.isBlocked,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'query': instance.query,
    };

_UserMoney _$UserMoneyFromJson(Map<String, dynamic> json) => _UserMoney(
  coins: (json['coins'] as num?)?.toInt(),
  gems:
      (json['gems'] as List<dynamic>?)
          ?.map((e) => UserMoneyGem.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$UserMoneyToJson(_UserMoney instance) =>
    <String, dynamic>{
      if (instance.coins case final value?) 'coins': value,
      if (instance.gems case final value?) 'gems': value,
    };

_UserMoneyGem _$UserMoneyGemFromJson(Map<String, dynamic> json) =>
    _UserMoneyGem(
      id: json['id'] as String?,
      value: (json['value'] as num?)?.toInt(),
      expiresAt: _dateTimeFromJson(json['expiresAt'] as String?),
      expirationInDays: (json['expirationInDays'] as num?)?.toInt(),
    );

Map<String, dynamic> _$UserMoneyGemToJson(
  _UserMoneyGem instance,
) => <String, dynamic>{
  if (instance.id case final value?) 'id': value,
  if (instance.value case final value?) 'value': value,
  if (_dateTimeToJson(instance.expiresAt) case final value?) 'expiresAt': value,
  if (instance.expirationInDays case final value?) 'expirationInDays': value,
};

_MoneyHistory _$MoneyHistoryFromJson(Map<String, dynamic> json) =>
    _MoneyHistory(
      id: json['id'] as String?,
      userId: json['userId'] as String?,
      coins: (json['coins'] as num?)?.toInt(),
      gems:
          (json['gems'] as List<dynamic>?)
              ?.map((e) => UserMoneyGem.fromJson(e as Map<String, dynamic>))
              .toList(),
      description: json['description'] as String?,
      timestamp:
          json['timestamp'] == null
              ? null
              : DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$MoneyHistoryToJson(_MoneyHistory instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.userId case final value?) 'userId': value,
      if (instance.coins case final value?) 'coins': value,
      if (instance.gems case final value?) 'gems': value,
      if (instance.description case final value?) 'description': value,
      if (instance.timestamp?.toIso8601String() case final value?)
        'timestamp': value,
    };
