import 'package:freezed_annotation/freezed_annotation.dart';

part 'user.freezed.dart';
part 'user.g.dart';

@freezed
abstract class User with _$User {
  @JsonSerializable(includeIfNull: false)
  factory User({
    String? id,
    String? email,
    UserRole? role,
    String? name,
    String? surname,
    UserMoney? money,
    DateTime? dateOfBirth,
    String? telephoneNumber,
    bool? isBlocked,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}

@freezed
abstract class UserFilter with _$UserFilter {
  factory UserFilter({
    String? id,
    String? email,
    UserRole? role,
    String? name,
    String? surname,
    UserMoney? money,
    DateTime? dateOfBirth,
    String? telephoneNumber,
    bool? isBlocked,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? query,
  }) = _UserFilter;

  factory UserFilter.fromJson(Map<String, dynamic> json) =>
      _$UserFilterFromJson(json);
}

enum UserRole {
  @JsonValue('admin')
  admin,
  @JsonValue('user')
  user;

  String getName() {
    switch (this) {
      case UserRole.admin:
        return 'Админ';
      case UserRole.user:
        return 'Пользователь';
    }
  }
}

enum UserMoneyEnum {
  coins,
  gems;

  String getName() {
    switch (this) {
      case UserMoneyEnum.coins:
        return 'Coins';
      case UserMoneyEnum.gems:
        return 'Gems';
    }
  }
}

@freezed
abstract class UserMoney with _$UserMoney {
  @JsonSerializable(includeIfNull: false)
  factory UserMoney({int? coins, List<UserMoneyGem>? gems}) = _UserMoney;

  factory UserMoney.fromJson(Map<String, dynamic> json) =>
      _$UserMoneyFromJson(json);
}

@freezed
abstract class UserMoneyGem with _$UserMoneyGem {
  @JsonSerializable(includeIfNull: false)
  factory UserMoneyGem({
    String? id,
    int? value,
    @JsonKey(toJson: _dateTimeToJson, fromJson: _dateTimeFromJson)
    DateTime? expiresAt,
    int? expirationInDays,
  }) = _UserMoneyGem;

  factory UserMoneyGem.fromJson(Map<String, dynamic> json) =>
      _$UserMoneyGemFromJson(json);
}

String? _dateTimeToJson(DateTime? date) => date?.toUtc().toIso8601String();

DateTime? _dateTimeFromJson(String? json) =>
    json != null ? DateTime.parse(json).toLocal() : null;

@freezed
abstract class MoneyHistory with _$MoneyHistory {
  @JsonSerializable(includeIfNull: false)
  factory MoneyHistory({
    String? id,
    String? userId,
    int? coins,
    List<UserMoneyGem>? gems,
    String? description,
    DateTime? timestamp,
  }) = _MoneyHistory;

  factory MoneyHistory.fromJson(Map<String, dynamic> json) =>
      _$MoneyHistoryFromJson(json);
}
