// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'index.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GetDevicesOutput {

 List<String>? get devices;
/// Create a copy of GetDevicesOutput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GetDevicesOutputCopyWith<GetDevicesOutput> get copyWith => _$GetDevicesOutputCopyWithImpl<GetDevicesOutput>(this as GetDevicesOutput, _$identity);

  /// Serializes this GetDevicesOutput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GetDevicesOutput&&const DeepCollectionEquality().equals(other.devices, devices));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(devices));

@override
String toString() {
  return 'GetDevicesOutput(devices: $devices)';
}


}

/// @nodoc
abstract mixin class $GetDevicesOutputCopyWith<$Res>  {
  factory $GetDevicesOutputCopyWith(GetDevicesOutput value, $Res Function(GetDevicesOutput) _then) = _$GetDevicesOutputCopyWithImpl;
@useResult
$Res call({
 List<String>? devices
});




}
/// @nodoc
class _$GetDevicesOutputCopyWithImpl<$Res>
    implements $GetDevicesOutputCopyWith<$Res> {
  _$GetDevicesOutputCopyWithImpl(this._self, this._then);

  final GetDevicesOutput _self;
  final $Res Function(GetDevicesOutput) _then;

/// Create a copy of GetDevicesOutput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? devices = freezed,}) {
  return _then(_self.copyWith(
devices: freezed == devices ? _self.devices : devices // ignore: cast_nullable_to_non_nullable
as List<String>?,
  ));
}

}


/// @nodoc

@JsonSerializable(includeIfNull: false)
class _GetDevicesOutput implements GetDevicesOutput {
   _GetDevicesOutput({final  List<String>? devices}): _devices = devices;
  factory _GetDevicesOutput.fromJson(Map<String, dynamic> json) => _$GetDevicesOutputFromJson(json);

 final  List<String>? _devices;
@override List<String>? get devices {
  final value = _devices;
  if (value == null) return null;
  if (_devices is EqualUnmodifiableListView) return _devices;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of GetDevicesOutput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GetDevicesOutputCopyWith<_GetDevicesOutput> get copyWith => __$GetDevicesOutputCopyWithImpl<_GetDevicesOutput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GetDevicesOutputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetDevicesOutput&&const DeepCollectionEquality().equals(other._devices, _devices));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_devices));

@override
String toString() {
  return 'GetDevicesOutput(devices: $devices)';
}


}

/// @nodoc
abstract mixin class _$GetDevicesOutputCopyWith<$Res> implements $GetDevicesOutputCopyWith<$Res> {
  factory _$GetDevicesOutputCopyWith(_GetDevicesOutput value, $Res Function(_GetDevicesOutput) _then) = __$GetDevicesOutputCopyWithImpl;
@override @useResult
$Res call({
 List<String>? devices
});




}
/// @nodoc
class __$GetDevicesOutputCopyWithImpl<$Res>
    implements _$GetDevicesOutputCopyWith<$Res> {
  __$GetDevicesOutputCopyWithImpl(this._self, this._then);

  final _GetDevicesOutput _self;
  final $Res Function(_GetDevicesOutput) _then;

/// Create a copy of GetDevicesOutput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? devices = freezed,}) {
  return _then(_GetDevicesOutput(
devices: freezed == devices ? _self._devices : devices // ignore: cast_nullable_to_non_nullable
as List<String>?,
  ));
}


}


/// @nodoc
mixin _$GetUserOutput {

 User? get user;
/// Create a copy of GetUserOutput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GetUserOutputCopyWith<GetUserOutput> get copyWith => _$GetUserOutputCopyWithImpl<GetUserOutput>(this as GetUserOutput, _$identity);

  /// Serializes this GetUserOutput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GetUserOutput&&(identical(other.user, user) || other.user == user));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,user);

@override
String toString() {
  return 'GetUserOutput(user: $user)';
}


}

/// @nodoc
abstract mixin class $GetUserOutputCopyWith<$Res>  {
  factory $GetUserOutputCopyWith(GetUserOutput value, $Res Function(GetUserOutput) _then) = _$GetUserOutputCopyWithImpl;
@useResult
$Res call({
 User? user
});


$UserCopyWith<$Res>? get user;

}
/// @nodoc
class _$GetUserOutputCopyWithImpl<$Res>
    implements $GetUserOutputCopyWith<$Res> {
  _$GetUserOutputCopyWithImpl(this._self, this._then);

  final GetUserOutput _self;
  final $Res Function(GetUserOutput) _then;

/// Create a copy of GetUserOutput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? user = freezed,}) {
  return _then(_self.copyWith(
user: freezed == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as User?,
  ));
}
/// Create a copy of GetUserOutput
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserCopyWith<$Res>? get user {
    if (_self.user == null) {
    return null;
  }

  return $UserCopyWith<$Res>(_self.user!, (value) {
    return _then(_self.copyWith(user: value));
  });
}
}


/// @nodoc

@JsonSerializable(includeIfNull: false)
class _GetUserOutput implements GetUserOutput {
   _GetUserOutput({this.user});
  factory _GetUserOutput.fromJson(Map<String, dynamic> json) => _$GetUserOutputFromJson(json);

@override final  User? user;

/// Create a copy of GetUserOutput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GetUserOutputCopyWith<_GetUserOutput> get copyWith => __$GetUserOutputCopyWithImpl<_GetUserOutput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GetUserOutputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetUserOutput&&(identical(other.user, user) || other.user == user));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,user);

@override
String toString() {
  return 'GetUserOutput(user: $user)';
}


}

/// @nodoc
abstract mixin class _$GetUserOutputCopyWith<$Res> implements $GetUserOutputCopyWith<$Res> {
  factory _$GetUserOutputCopyWith(_GetUserOutput value, $Res Function(_GetUserOutput) _then) = __$GetUserOutputCopyWithImpl;
@override @useResult
$Res call({
 User? user
});


@override $UserCopyWith<$Res>? get user;

}
/// @nodoc
class __$GetUserOutputCopyWithImpl<$Res>
    implements _$GetUserOutputCopyWith<$Res> {
  __$GetUserOutputCopyWithImpl(this._self, this._then);

  final _GetUserOutput _self;
  final $Res Function(_GetUserOutput) _then;

/// Create a copy of GetUserOutput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? user = freezed,}) {
  return _then(_GetUserOutput(
user: freezed == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as User?,
  ));
}

/// Create a copy of GetUserOutput
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserCopyWith<$Res>? get user {
    if (_self.user == null) {
    return null;
  }

  return $UserCopyWith<$Res>(_self.user!, (value) {
    return _then(_self.copyWith(user: value));
  });
}
}


/// @nodoc
mixin _$GetUsersInput {

 UserFilter? get filter; Pagination? get pagination;
/// Create a copy of GetUsersInput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GetUsersInputCopyWith<GetUsersInput> get copyWith => _$GetUsersInputCopyWithImpl<GetUsersInput>(this as GetUsersInput, _$identity);

  /// Serializes this GetUsersInput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GetUsersInput&&(identical(other.filter, filter) || other.filter == filter)&&(identical(other.pagination, pagination) || other.pagination == pagination));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,filter,pagination);

@override
String toString() {
  return 'GetUsersInput(filter: $filter, pagination: $pagination)';
}


}

/// @nodoc
abstract mixin class $GetUsersInputCopyWith<$Res>  {
  factory $GetUsersInputCopyWith(GetUsersInput value, $Res Function(GetUsersInput) _then) = _$GetUsersInputCopyWithImpl;
@useResult
$Res call({
 UserFilter? filter, Pagination? pagination
});


$UserFilterCopyWith<$Res>? get filter;$PaginationCopyWith<$Res>? get pagination;

}
/// @nodoc
class _$GetUsersInputCopyWithImpl<$Res>
    implements $GetUsersInputCopyWith<$Res> {
  _$GetUsersInputCopyWithImpl(this._self, this._then);

  final GetUsersInput _self;
  final $Res Function(GetUsersInput) _then;

/// Create a copy of GetUsersInput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? filter = freezed,Object? pagination = freezed,}) {
  return _then(_self.copyWith(
filter: freezed == filter ? _self.filter : filter // ignore: cast_nullable_to_non_nullable
as UserFilter?,pagination: freezed == pagination ? _self.pagination : pagination // ignore: cast_nullable_to_non_nullable
as Pagination?,
  ));
}
/// Create a copy of GetUsersInput
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserFilterCopyWith<$Res>? get filter {
    if (_self.filter == null) {
    return null;
  }

  return $UserFilterCopyWith<$Res>(_self.filter!, (value) {
    return _then(_self.copyWith(filter: value));
  });
}/// Create a copy of GetUsersInput
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaginationCopyWith<$Res>? get pagination {
    if (_self.pagination == null) {
    return null;
  }

  return $PaginationCopyWith<$Res>(_self.pagination!, (value) {
    return _then(_self.copyWith(pagination: value));
  });
}
}


/// @nodoc

@JsonSerializable(includeIfNull: false)
class _GetUsersInput implements GetUsersInput {
   _GetUsersInput({this.filter, this.pagination});
  factory _GetUsersInput.fromJson(Map<String, dynamic> json) => _$GetUsersInputFromJson(json);

@override final  UserFilter? filter;
@override final  Pagination? pagination;

/// Create a copy of GetUsersInput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GetUsersInputCopyWith<_GetUsersInput> get copyWith => __$GetUsersInputCopyWithImpl<_GetUsersInput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GetUsersInputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetUsersInput&&(identical(other.filter, filter) || other.filter == filter)&&(identical(other.pagination, pagination) || other.pagination == pagination));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,filter,pagination);

@override
String toString() {
  return 'GetUsersInput(filter: $filter, pagination: $pagination)';
}


}

/// @nodoc
abstract mixin class _$GetUsersInputCopyWith<$Res> implements $GetUsersInputCopyWith<$Res> {
  factory _$GetUsersInputCopyWith(_GetUsersInput value, $Res Function(_GetUsersInput) _then) = __$GetUsersInputCopyWithImpl;
@override @useResult
$Res call({
 UserFilter? filter, Pagination? pagination
});


@override $UserFilterCopyWith<$Res>? get filter;@override $PaginationCopyWith<$Res>? get pagination;

}
/// @nodoc
class __$GetUsersInputCopyWithImpl<$Res>
    implements _$GetUsersInputCopyWith<$Res> {
  __$GetUsersInputCopyWithImpl(this._self, this._then);

  final _GetUsersInput _self;
  final $Res Function(_GetUsersInput) _then;

/// Create a copy of GetUsersInput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? filter = freezed,Object? pagination = freezed,}) {
  return _then(_GetUsersInput(
filter: freezed == filter ? _self.filter : filter // ignore: cast_nullable_to_non_nullable
as UserFilter?,pagination: freezed == pagination ? _self.pagination : pagination // ignore: cast_nullable_to_non_nullable
as Pagination?,
  ));
}

/// Create a copy of GetUsersInput
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserFilterCopyWith<$Res>? get filter {
    if (_self.filter == null) {
    return null;
  }

  return $UserFilterCopyWith<$Res>(_self.filter!, (value) {
    return _then(_self.copyWith(filter: value));
  });
}/// Create a copy of GetUsersInput
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaginationCopyWith<$Res>? get pagination {
    if (_self.pagination == null) {
    return null;
  }

  return $PaginationCopyWith<$Res>(_self.pagination!, (value) {
    return _then(_self.copyWith(pagination: value));
  });
}
}


/// @nodoc
mixin _$GetUsersOutput {

 List<User>? get data; int? get totalItems;
/// Create a copy of GetUsersOutput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GetUsersOutputCopyWith<GetUsersOutput> get copyWith => _$GetUsersOutputCopyWithImpl<GetUsersOutput>(this as GetUsersOutput, _$identity);

  /// Serializes this GetUsersOutput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GetUsersOutput&&const DeepCollectionEquality().equals(other.data, data)&&(identical(other.totalItems, totalItems) || other.totalItems == totalItems));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(data),totalItems);

@override
String toString() {
  return 'GetUsersOutput(data: $data, totalItems: $totalItems)';
}


}

/// @nodoc
abstract mixin class $GetUsersOutputCopyWith<$Res>  {
  factory $GetUsersOutputCopyWith(GetUsersOutput value, $Res Function(GetUsersOutput) _then) = _$GetUsersOutputCopyWithImpl;
@useResult
$Res call({
 List<User>? data, int? totalItems
});




}
/// @nodoc
class _$GetUsersOutputCopyWithImpl<$Res>
    implements $GetUsersOutputCopyWith<$Res> {
  _$GetUsersOutputCopyWithImpl(this._self, this._then);

  final GetUsersOutput _self;
  final $Res Function(GetUsersOutput) _then;

/// Create a copy of GetUsersOutput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? data = freezed,Object? totalItems = freezed,}) {
  return _then(_self.copyWith(
data: freezed == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as List<User>?,totalItems: freezed == totalItems ? _self.totalItems : totalItems // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc

@JsonSerializable(includeIfNull: false)
class _GetUsersOutput implements GetUsersOutput {
   _GetUsersOutput({final  List<User>? data, this.totalItems}): _data = data;
  factory _GetUsersOutput.fromJson(Map<String, dynamic> json) => _$GetUsersOutputFromJson(json);

 final  List<User>? _data;
@override List<User>? get data {
  final value = _data;
  if (value == null) return null;
  if (_data is EqualUnmodifiableListView) return _data;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  int? totalItems;

/// Create a copy of GetUsersOutput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GetUsersOutputCopyWith<_GetUsersOutput> get copyWith => __$GetUsersOutputCopyWithImpl<_GetUsersOutput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GetUsersOutputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetUsersOutput&&const DeepCollectionEquality().equals(other._data, _data)&&(identical(other.totalItems, totalItems) || other.totalItems == totalItems));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_data),totalItems);

@override
String toString() {
  return 'GetUsersOutput(data: $data, totalItems: $totalItems)';
}


}

/// @nodoc
abstract mixin class _$GetUsersOutputCopyWith<$Res> implements $GetUsersOutputCopyWith<$Res> {
  factory _$GetUsersOutputCopyWith(_GetUsersOutput value, $Res Function(_GetUsersOutput) _then) = __$GetUsersOutputCopyWithImpl;
@override @useResult
$Res call({
 List<User>? data, int? totalItems
});




}
/// @nodoc
class __$GetUsersOutputCopyWithImpl<$Res>
    implements _$GetUsersOutputCopyWith<$Res> {
  __$GetUsersOutputCopyWithImpl(this._self, this._then);

  final _GetUsersOutput _self;
  final $Res Function(_GetUsersOutput) _then;

/// Create a copy of GetUsersOutput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? data = freezed,Object? totalItems = freezed,}) {
  return _then(_GetUsersOutput(
data: freezed == data ? _self._data : data // ignore: cast_nullable_to_non_nullable
as List<User>?,totalItems: freezed == totalItems ? _self.totalItems : totalItems // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}


/// @nodoc
mixin _$GetMoneyHistoryInput {

 String? get userId; Pagination? get pagination;
/// Create a copy of GetMoneyHistoryInput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GetMoneyHistoryInputCopyWith<GetMoneyHistoryInput> get copyWith => _$GetMoneyHistoryInputCopyWithImpl<GetMoneyHistoryInput>(this as GetMoneyHistoryInput, _$identity);

  /// Serializes this GetMoneyHistoryInput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GetMoneyHistoryInput&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.pagination, pagination) || other.pagination == pagination));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,pagination);

@override
String toString() {
  return 'GetMoneyHistoryInput(userId: $userId, pagination: $pagination)';
}


}

/// @nodoc
abstract mixin class $GetMoneyHistoryInputCopyWith<$Res>  {
  factory $GetMoneyHistoryInputCopyWith(GetMoneyHistoryInput value, $Res Function(GetMoneyHistoryInput) _then) = _$GetMoneyHistoryInputCopyWithImpl;
@useResult
$Res call({
 String? userId, Pagination? pagination
});


$PaginationCopyWith<$Res>? get pagination;

}
/// @nodoc
class _$GetMoneyHistoryInputCopyWithImpl<$Res>
    implements $GetMoneyHistoryInputCopyWith<$Res> {
  _$GetMoneyHistoryInputCopyWithImpl(this._self, this._then);

  final GetMoneyHistoryInput _self;
  final $Res Function(GetMoneyHistoryInput) _then;

/// Create a copy of GetMoneyHistoryInput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? userId = freezed,Object? pagination = freezed,}) {
  return _then(_self.copyWith(
userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String?,pagination: freezed == pagination ? _self.pagination : pagination // ignore: cast_nullable_to_non_nullable
as Pagination?,
  ));
}
/// Create a copy of GetMoneyHistoryInput
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaginationCopyWith<$Res>? get pagination {
    if (_self.pagination == null) {
    return null;
  }

  return $PaginationCopyWith<$Res>(_self.pagination!, (value) {
    return _then(_self.copyWith(pagination: value));
  });
}
}


/// @nodoc

@JsonSerializable(includeIfNull: false)
class _GetMoneyHistoryInput implements GetMoneyHistoryInput {
   _GetMoneyHistoryInput({this.userId, this.pagination});
  factory _GetMoneyHistoryInput.fromJson(Map<String, dynamic> json) => _$GetMoneyHistoryInputFromJson(json);

@override final  String? userId;
@override final  Pagination? pagination;

/// Create a copy of GetMoneyHistoryInput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GetMoneyHistoryInputCopyWith<_GetMoneyHistoryInput> get copyWith => __$GetMoneyHistoryInputCopyWithImpl<_GetMoneyHistoryInput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GetMoneyHistoryInputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetMoneyHistoryInput&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.pagination, pagination) || other.pagination == pagination));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,pagination);

@override
String toString() {
  return 'GetMoneyHistoryInput(userId: $userId, pagination: $pagination)';
}


}

/// @nodoc
abstract mixin class _$GetMoneyHistoryInputCopyWith<$Res> implements $GetMoneyHistoryInputCopyWith<$Res> {
  factory _$GetMoneyHistoryInputCopyWith(_GetMoneyHistoryInput value, $Res Function(_GetMoneyHistoryInput) _then) = __$GetMoneyHistoryInputCopyWithImpl;
@override @useResult
$Res call({
 String? userId, Pagination? pagination
});


@override $PaginationCopyWith<$Res>? get pagination;

}
/// @nodoc
class __$GetMoneyHistoryInputCopyWithImpl<$Res>
    implements _$GetMoneyHistoryInputCopyWith<$Res> {
  __$GetMoneyHistoryInputCopyWithImpl(this._self, this._then);

  final _GetMoneyHistoryInput _self;
  final $Res Function(_GetMoneyHistoryInput) _then;

/// Create a copy of GetMoneyHistoryInput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? userId = freezed,Object? pagination = freezed,}) {
  return _then(_GetMoneyHistoryInput(
userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String?,pagination: freezed == pagination ? _self.pagination : pagination // ignore: cast_nullable_to_non_nullable
as Pagination?,
  ));
}

/// Create a copy of GetMoneyHistoryInput
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaginationCopyWith<$Res>? get pagination {
    if (_self.pagination == null) {
    return null;
  }

  return $PaginationCopyWith<$Res>(_self.pagination!, (value) {
    return _then(_self.copyWith(pagination: value));
  });
}
}


/// @nodoc
mixin _$GetMoneyHistoryOutput {

 List<MoneyHistory>? get items; int? get totalItems;
/// Create a copy of GetMoneyHistoryOutput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GetMoneyHistoryOutputCopyWith<GetMoneyHistoryOutput> get copyWith => _$GetMoneyHistoryOutputCopyWithImpl<GetMoneyHistoryOutput>(this as GetMoneyHistoryOutput, _$identity);

  /// Serializes this GetMoneyHistoryOutput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GetMoneyHistoryOutput&&const DeepCollectionEquality().equals(other.items, items)&&(identical(other.totalItems, totalItems) || other.totalItems == totalItems));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(items),totalItems);

@override
String toString() {
  return 'GetMoneyHistoryOutput(items: $items, totalItems: $totalItems)';
}


}

/// @nodoc
abstract mixin class $GetMoneyHistoryOutputCopyWith<$Res>  {
  factory $GetMoneyHistoryOutputCopyWith(GetMoneyHistoryOutput value, $Res Function(GetMoneyHistoryOutput) _then) = _$GetMoneyHistoryOutputCopyWithImpl;
@useResult
$Res call({
 List<MoneyHistory>? items, int? totalItems
});




}
/// @nodoc
class _$GetMoneyHistoryOutputCopyWithImpl<$Res>
    implements $GetMoneyHistoryOutputCopyWith<$Res> {
  _$GetMoneyHistoryOutputCopyWithImpl(this._self, this._then);

  final GetMoneyHistoryOutput _self;
  final $Res Function(GetMoneyHistoryOutput) _then;

/// Create a copy of GetMoneyHistoryOutput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? items = freezed,Object? totalItems = freezed,}) {
  return _then(_self.copyWith(
items: freezed == items ? _self.items : items // ignore: cast_nullable_to_non_nullable
as List<MoneyHistory>?,totalItems: freezed == totalItems ? _self.totalItems : totalItems // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _GetMoneyHistoryOutput implements GetMoneyHistoryOutput {
   _GetMoneyHistoryOutput({final  List<MoneyHistory>? items, this.totalItems}): _items = items;
  factory _GetMoneyHistoryOutput.fromJson(Map<String, dynamic> json) => _$GetMoneyHistoryOutputFromJson(json);

 final  List<MoneyHistory>? _items;
@override List<MoneyHistory>? get items {
  final value = _items;
  if (value == null) return null;
  if (_items is EqualUnmodifiableListView) return _items;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  int? totalItems;

/// Create a copy of GetMoneyHistoryOutput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GetMoneyHistoryOutputCopyWith<_GetMoneyHistoryOutput> get copyWith => __$GetMoneyHistoryOutputCopyWithImpl<_GetMoneyHistoryOutput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GetMoneyHistoryOutputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetMoneyHistoryOutput&&const DeepCollectionEquality().equals(other._items, _items)&&(identical(other.totalItems, totalItems) || other.totalItems == totalItems));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_items),totalItems);

@override
String toString() {
  return 'GetMoneyHistoryOutput(items: $items, totalItems: $totalItems)';
}


}

/// @nodoc
abstract mixin class _$GetMoneyHistoryOutputCopyWith<$Res> implements $GetMoneyHistoryOutputCopyWith<$Res> {
  factory _$GetMoneyHistoryOutputCopyWith(_GetMoneyHistoryOutput value, $Res Function(_GetMoneyHistoryOutput) _then) = __$GetMoneyHistoryOutputCopyWithImpl;
@override @useResult
$Res call({
 List<MoneyHistory>? items, int? totalItems
});




}
/// @nodoc
class __$GetMoneyHistoryOutputCopyWithImpl<$Res>
    implements _$GetMoneyHistoryOutputCopyWith<$Res> {
  __$GetMoneyHistoryOutputCopyWithImpl(this._self, this._then);

  final _GetMoneyHistoryOutput _self;
  final $Res Function(_GetMoneyHistoryOutput) _then;

/// Create a copy of GetMoneyHistoryOutput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? items = freezed,Object? totalItems = freezed,}) {
  return _then(_GetMoneyHistoryOutput(
items: freezed == items ? _self._items : items // ignore: cast_nullable_to_non_nullable
as List<MoneyHistory>?,totalItems: freezed == totalItems ? _self.totalItems : totalItems // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on
