// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$User {

 String? get id; String? get email; UserRole? get role; String? get name; String? get surname; UserMoney? get money; DateTime? get dateOfBirth; String? get telephoneNumber; bool? get isBlocked; DateTime? get createdAt; DateTime? get updatedAt;
/// Create a copy of User
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserCopyWith<User> get copyWith => _$UserCopyWithImpl<User>(this as User, _$identity);

  /// Serializes this User to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is User&&(identical(other.id, id) || other.id == id)&&(identical(other.email, email) || other.email == email)&&(identical(other.role, role) || other.role == role)&&(identical(other.name, name) || other.name == name)&&(identical(other.surname, surname) || other.surname == surname)&&(identical(other.money, money) || other.money == money)&&(identical(other.dateOfBirth, dateOfBirth) || other.dateOfBirth == dateOfBirth)&&(identical(other.telephoneNumber, telephoneNumber) || other.telephoneNumber == telephoneNumber)&&(identical(other.isBlocked, isBlocked) || other.isBlocked == isBlocked)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,email,role,name,surname,money,dateOfBirth,telephoneNumber,isBlocked,createdAt,updatedAt);

@override
String toString() {
  return 'User(id: $id, email: $email, role: $role, name: $name, surname: $surname, money: $money, dateOfBirth: $dateOfBirth, telephoneNumber: $telephoneNumber, isBlocked: $isBlocked, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $UserCopyWith<$Res>  {
  factory $UserCopyWith(User value, $Res Function(User) _then) = _$UserCopyWithImpl;
@useResult
$Res call({
 String? id, String? email, UserRole? role, String? name, String? surname, UserMoney? money, DateTime? dateOfBirth, String? telephoneNumber, bool? isBlocked, DateTime? createdAt, DateTime? updatedAt
});


$UserMoneyCopyWith<$Res>? get money;

}
/// @nodoc
class _$UserCopyWithImpl<$Res>
    implements $UserCopyWith<$Res> {
  _$UserCopyWithImpl(this._self, this._then);

  final User _self;
  final $Res Function(User) _then;

/// Create a copy of User
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? email = freezed,Object? role = freezed,Object? name = freezed,Object? surname = freezed,Object? money = freezed,Object? dateOfBirth = freezed,Object? telephoneNumber = freezed,Object? isBlocked = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,role: freezed == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as UserRole?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,surname: freezed == surname ? _self.surname : surname // ignore: cast_nullable_to_non_nullable
as String?,money: freezed == money ? _self.money : money // ignore: cast_nullable_to_non_nullable
as UserMoney?,dateOfBirth: freezed == dateOfBirth ? _self.dateOfBirth : dateOfBirth // ignore: cast_nullable_to_non_nullable
as DateTime?,telephoneNumber: freezed == telephoneNumber ? _self.telephoneNumber : telephoneNumber // ignore: cast_nullable_to_non_nullable
as String?,isBlocked: freezed == isBlocked ? _self.isBlocked : isBlocked // ignore: cast_nullable_to_non_nullable
as bool?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}
/// Create a copy of User
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserMoneyCopyWith<$Res>? get money {
    if (_self.money == null) {
    return null;
  }

  return $UserMoneyCopyWith<$Res>(_self.money!, (value) {
    return _then(_self.copyWith(money: value));
  });
}
}


/// @nodoc

@JsonSerializable(includeIfNull: false)
class _User implements User {
   _User({this.id, this.email, this.role, this.name, this.surname, this.money, this.dateOfBirth, this.telephoneNumber, this.isBlocked, this.createdAt, this.updatedAt});
  factory _User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

@override final  String? id;
@override final  String? email;
@override final  UserRole? role;
@override final  String? name;
@override final  String? surname;
@override final  UserMoney? money;
@override final  DateTime? dateOfBirth;
@override final  String? telephoneNumber;
@override final  bool? isBlocked;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;

/// Create a copy of User
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserCopyWith<_User> get copyWith => __$UserCopyWithImpl<_User>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _User&&(identical(other.id, id) || other.id == id)&&(identical(other.email, email) || other.email == email)&&(identical(other.role, role) || other.role == role)&&(identical(other.name, name) || other.name == name)&&(identical(other.surname, surname) || other.surname == surname)&&(identical(other.money, money) || other.money == money)&&(identical(other.dateOfBirth, dateOfBirth) || other.dateOfBirth == dateOfBirth)&&(identical(other.telephoneNumber, telephoneNumber) || other.telephoneNumber == telephoneNumber)&&(identical(other.isBlocked, isBlocked) || other.isBlocked == isBlocked)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,email,role,name,surname,money,dateOfBirth,telephoneNumber,isBlocked,createdAt,updatedAt);

@override
String toString() {
  return 'User(id: $id, email: $email, role: $role, name: $name, surname: $surname, money: $money, dateOfBirth: $dateOfBirth, telephoneNumber: $telephoneNumber, isBlocked: $isBlocked, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$UserCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$UserCopyWith(_User value, $Res Function(_User) _then) = __$UserCopyWithImpl;
@override @useResult
$Res call({
 String? id, String? email, UserRole? role, String? name, String? surname, UserMoney? money, DateTime? dateOfBirth, String? telephoneNumber, bool? isBlocked, DateTime? createdAt, DateTime? updatedAt
});


@override $UserMoneyCopyWith<$Res>? get money;

}
/// @nodoc
class __$UserCopyWithImpl<$Res>
    implements _$UserCopyWith<$Res> {
  __$UserCopyWithImpl(this._self, this._then);

  final _User _self;
  final $Res Function(_User) _then;

/// Create a copy of User
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? email = freezed,Object? role = freezed,Object? name = freezed,Object? surname = freezed,Object? money = freezed,Object? dateOfBirth = freezed,Object? telephoneNumber = freezed,Object? isBlocked = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_User(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,role: freezed == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as UserRole?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,surname: freezed == surname ? _self.surname : surname // ignore: cast_nullable_to_non_nullable
as String?,money: freezed == money ? _self.money : money // ignore: cast_nullable_to_non_nullable
as UserMoney?,dateOfBirth: freezed == dateOfBirth ? _self.dateOfBirth : dateOfBirth // ignore: cast_nullable_to_non_nullable
as DateTime?,telephoneNumber: freezed == telephoneNumber ? _self.telephoneNumber : telephoneNumber // ignore: cast_nullable_to_non_nullable
as String?,isBlocked: freezed == isBlocked ? _self.isBlocked : isBlocked // ignore: cast_nullable_to_non_nullable
as bool?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

/// Create a copy of User
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserMoneyCopyWith<$Res>? get money {
    if (_self.money == null) {
    return null;
  }

  return $UserMoneyCopyWith<$Res>(_self.money!, (value) {
    return _then(_self.copyWith(money: value));
  });
}
}


/// @nodoc
mixin _$UserFilter {

 String? get id; String? get email; UserRole? get role; String? get name; String? get surname; UserMoney? get money; DateTime? get dateOfBirth; String? get telephoneNumber; bool? get isBlocked; DateTime? get createdAt; DateTime? get updatedAt; String? get query;
/// Create a copy of UserFilter
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserFilterCopyWith<UserFilter> get copyWith => _$UserFilterCopyWithImpl<UserFilter>(this as UserFilter, _$identity);

  /// Serializes this UserFilter to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserFilter&&(identical(other.id, id) || other.id == id)&&(identical(other.email, email) || other.email == email)&&(identical(other.role, role) || other.role == role)&&(identical(other.name, name) || other.name == name)&&(identical(other.surname, surname) || other.surname == surname)&&(identical(other.money, money) || other.money == money)&&(identical(other.dateOfBirth, dateOfBirth) || other.dateOfBirth == dateOfBirth)&&(identical(other.telephoneNumber, telephoneNumber) || other.telephoneNumber == telephoneNumber)&&(identical(other.isBlocked, isBlocked) || other.isBlocked == isBlocked)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.query, query) || other.query == query));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,email,role,name,surname,money,dateOfBirth,telephoneNumber,isBlocked,createdAt,updatedAt,query);

@override
String toString() {
  return 'UserFilter(id: $id, email: $email, role: $role, name: $name, surname: $surname, money: $money, dateOfBirth: $dateOfBirth, telephoneNumber: $telephoneNumber, isBlocked: $isBlocked, createdAt: $createdAt, updatedAt: $updatedAt, query: $query)';
}


}

/// @nodoc
abstract mixin class $UserFilterCopyWith<$Res>  {
  factory $UserFilterCopyWith(UserFilter value, $Res Function(UserFilter) _then) = _$UserFilterCopyWithImpl;
@useResult
$Res call({
 String? id, String? email, UserRole? role, String? name, String? surname, UserMoney? money, DateTime? dateOfBirth, String? telephoneNumber, bool? isBlocked, DateTime? createdAt, DateTime? updatedAt, String? query
});


$UserMoneyCopyWith<$Res>? get money;

}
/// @nodoc
class _$UserFilterCopyWithImpl<$Res>
    implements $UserFilterCopyWith<$Res> {
  _$UserFilterCopyWithImpl(this._self, this._then);

  final UserFilter _self;
  final $Res Function(UserFilter) _then;

/// Create a copy of UserFilter
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? email = freezed,Object? role = freezed,Object? name = freezed,Object? surname = freezed,Object? money = freezed,Object? dateOfBirth = freezed,Object? telephoneNumber = freezed,Object? isBlocked = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? query = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,role: freezed == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as UserRole?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,surname: freezed == surname ? _self.surname : surname // ignore: cast_nullable_to_non_nullable
as String?,money: freezed == money ? _self.money : money // ignore: cast_nullable_to_non_nullable
as UserMoney?,dateOfBirth: freezed == dateOfBirth ? _self.dateOfBirth : dateOfBirth // ignore: cast_nullable_to_non_nullable
as DateTime?,telephoneNumber: freezed == telephoneNumber ? _self.telephoneNumber : telephoneNumber // ignore: cast_nullable_to_non_nullable
as String?,isBlocked: freezed == isBlocked ? _self.isBlocked : isBlocked // ignore: cast_nullable_to_non_nullable
as bool?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,query: freezed == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of UserFilter
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserMoneyCopyWith<$Res>? get money {
    if (_self.money == null) {
    return null;
  }

  return $UserMoneyCopyWith<$Res>(_self.money!, (value) {
    return _then(_self.copyWith(money: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _UserFilter implements UserFilter {
   _UserFilter({this.id, this.email, this.role, this.name, this.surname, this.money, this.dateOfBirth, this.telephoneNumber, this.isBlocked, this.createdAt, this.updatedAt, this.query});
  factory _UserFilter.fromJson(Map<String, dynamic> json) => _$UserFilterFromJson(json);

@override final  String? id;
@override final  String? email;
@override final  UserRole? role;
@override final  String? name;
@override final  String? surname;
@override final  UserMoney? money;
@override final  DateTime? dateOfBirth;
@override final  String? telephoneNumber;
@override final  bool? isBlocked;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;
@override final  String? query;

/// Create a copy of UserFilter
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserFilterCopyWith<_UserFilter> get copyWith => __$UserFilterCopyWithImpl<_UserFilter>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserFilterToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserFilter&&(identical(other.id, id) || other.id == id)&&(identical(other.email, email) || other.email == email)&&(identical(other.role, role) || other.role == role)&&(identical(other.name, name) || other.name == name)&&(identical(other.surname, surname) || other.surname == surname)&&(identical(other.money, money) || other.money == money)&&(identical(other.dateOfBirth, dateOfBirth) || other.dateOfBirth == dateOfBirth)&&(identical(other.telephoneNumber, telephoneNumber) || other.telephoneNumber == telephoneNumber)&&(identical(other.isBlocked, isBlocked) || other.isBlocked == isBlocked)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.query, query) || other.query == query));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,email,role,name,surname,money,dateOfBirth,telephoneNumber,isBlocked,createdAt,updatedAt,query);

@override
String toString() {
  return 'UserFilter(id: $id, email: $email, role: $role, name: $name, surname: $surname, money: $money, dateOfBirth: $dateOfBirth, telephoneNumber: $telephoneNumber, isBlocked: $isBlocked, createdAt: $createdAt, updatedAt: $updatedAt, query: $query)';
}


}

/// @nodoc
abstract mixin class _$UserFilterCopyWith<$Res> implements $UserFilterCopyWith<$Res> {
  factory _$UserFilterCopyWith(_UserFilter value, $Res Function(_UserFilter) _then) = __$UserFilterCopyWithImpl;
@override @useResult
$Res call({
 String? id, String? email, UserRole? role, String? name, String? surname, UserMoney? money, DateTime? dateOfBirth, String? telephoneNumber, bool? isBlocked, DateTime? createdAt, DateTime? updatedAt, String? query
});


@override $UserMoneyCopyWith<$Res>? get money;

}
/// @nodoc
class __$UserFilterCopyWithImpl<$Res>
    implements _$UserFilterCopyWith<$Res> {
  __$UserFilterCopyWithImpl(this._self, this._then);

  final _UserFilter _self;
  final $Res Function(_UserFilter) _then;

/// Create a copy of UserFilter
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? email = freezed,Object? role = freezed,Object? name = freezed,Object? surname = freezed,Object? money = freezed,Object? dateOfBirth = freezed,Object? telephoneNumber = freezed,Object? isBlocked = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? query = freezed,}) {
  return _then(_UserFilter(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,role: freezed == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as UserRole?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,surname: freezed == surname ? _self.surname : surname // ignore: cast_nullable_to_non_nullable
as String?,money: freezed == money ? _self.money : money // ignore: cast_nullable_to_non_nullable
as UserMoney?,dateOfBirth: freezed == dateOfBirth ? _self.dateOfBirth : dateOfBirth // ignore: cast_nullable_to_non_nullable
as DateTime?,telephoneNumber: freezed == telephoneNumber ? _self.telephoneNumber : telephoneNumber // ignore: cast_nullable_to_non_nullable
as String?,isBlocked: freezed == isBlocked ? _self.isBlocked : isBlocked // ignore: cast_nullable_to_non_nullable
as bool?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,query: freezed == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of UserFilter
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserMoneyCopyWith<$Res>? get money {
    if (_self.money == null) {
    return null;
  }

  return $UserMoneyCopyWith<$Res>(_self.money!, (value) {
    return _then(_self.copyWith(money: value));
  });
}
}


/// @nodoc
mixin _$UserMoney {

 int? get coins; List<UserMoneyGem>? get gems;
/// Create a copy of UserMoney
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserMoneyCopyWith<UserMoney> get copyWith => _$UserMoneyCopyWithImpl<UserMoney>(this as UserMoney, _$identity);

  /// Serializes this UserMoney to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserMoney&&(identical(other.coins, coins) || other.coins == coins)&&const DeepCollectionEquality().equals(other.gems, gems));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,coins,const DeepCollectionEquality().hash(gems));

@override
String toString() {
  return 'UserMoney(coins: $coins, gems: $gems)';
}


}

/// @nodoc
abstract mixin class $UserMoneyCopyWith<$Res>  {
  factory $UserMoneyCopyWith(UserMoney value, $Res Function(UserMoney) _then) = _$UserMoneyCopyWithImpl;
@useResult
$Res call({
 int? coins, List<UserMoneyGem>? gems
});




}
/// @nodoc
class _$UserMoneyCopyWithImpl<$Res>
    implements $UserMoneyCopyWith<$Res> {
  _$UserMoneyCopyWithImpl(this._self, this._then);

  final UserMoney _self;
  final $Res Function(UserMoney) _then;

/// Create a copy of UserMoney
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? coins = freezed,Object? gems = freezed,}) {
  return _then(_self.copyWith(
coins: freezed == coins ? _self.coins : coins // ignore: cast_nullable_to_non_nullable
as int?,gems: freezed == gems ? _self.gems : gems // ignore: cast_nullable_to_non_nullable
as List<UserMoneyGem>?,
  ));
}

}


/// @nodoc

@JsonSerializable(includeIfNull: false)
class _UserMoney implements UserMoney {
   _UserMoney({this.coins, final  List<UserMoneyGem>? gems}): _gems = gems;
  factory _UserMoney.fromJson(Map<String, dynamic> json) => _$UserMoneyFromJson(json);

@override final  int? coins;
 final  List<UserMoneyGem>? _gems;
@override List<UserMoneyGem>? get gems {
  final value = _gems;
  if (value == null) return null;
  if (_gems is EqualUnmodifiableListView) return _gems;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of UserMoney
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserMoneyCopyWith<_UserMoney> get copyWith => __$UserMoneyCopyWithImpl<_UserMoney>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserMoneyToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserMoney&&(identical(other.coins, coins) || other.coins == coins)&&const DeepCollectionEquality().equals(other._gems, _gems));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,coins,const DeepCollectionEquality().hash(_gems));

@override
String toString() {
  return 'UserMoney(coins: $coins, gems: $gems)';
}


}

/// @nodoc
abstract mixin class _$UserMoneyCopyWith<$Res> implements $UserMoneyCopyWith<$Res> {
  factory _$UserMoneyCopyWith(_UserMoney value, $Res Function(_UserMoney) _then) = __$UserMoneyCopyWithImpl;
@override @useResult
$Res call({
 int? coins, List<UserMoneyGem>? gems
});




}
/// @nodoc
class __$UserMoneyCopyWithImpl<$Res>
    implements _$UserMoneyCopyWith<$Res> {
  __$UserMoneyCopyWithImpl(this._self, this._then);

  final _UserMoney _self;
  final $Res Function(_UserMoney) _then;

/// Create a copy of UserMoney
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? coins = freezed,Object? gems = freezed,}) {
  return _then(_UserMoney(
coins: freezed == coins ? _self.coins : coins // ignore: cast_nullable_to_non_nullable
as int?,gems: freezed == gems ? _self._gems : gems // ignore: cast_nullable_to_non_nullable
as List<UserMoneyGem>?,
  ));
}


}


/// @nodoc
mixin _$UserMoneyGem {

 String? get id; int? get value;@JsonKey(toJson: _dateTimeToJson, fromJson: _dateTimeFromJson) DateTime? get expiresAt; int? get expirationInDays;
/// Create a copy of UserMoneyGem
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserMoneyGemCopyWith<UserMoneyGem> get copyWith => _$UserMoneyGemCopyWithImpl<UserMoneyGem>(this as UserMoneyGem, _$identity);

  /// Serializes this UserMoneyGem to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserMoneyGem&&(identical(other.id, id) || other.id == id)&&(identical(other.value, value) || other.value == value)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&(identical(other.expirationInDays, expirationInDays) || other.expirationInDays == expirationInDays));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,value,expiresAt,expirationInDays);

@override
String toString() {
  return 'UserMoneyGem(id: $id, value: $value, expiresAt: $expiresAt, expirationInDays: $expirationInDays)';
}


}

/// @nodoc
abstract mixin class $UserMoneyGemCopyWith<$Res>  {
  factory $UserMoneyGemCopyWith(UserMoneyGem value, $Res Function(UserMoneyGem) _then) = _$UserMoneyGemCopyWithImpl;
@useResult
$Res call({
 String? id, int? value,@JsonKey(toJson: _dateTimeToJson, fromJson: _dateTimeFromJson) DateTime? expiresAt, int? expirationInDays
});




}
/// @nodoc
class _$UserMoneyGemCopyWithImpl<$Res>
    implements $UserMoneyGemCopyWith<$Res> {
  _$UserMoneyGemCopyWithImpl(this._self, this._then);

  final UserMoneyGem _self;
  final $Res Function(UserMoneyGem) _then;

/// Create a copy of UserMoneyGem
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? value = freezed,Object? expiresAt = freezed,Object? expirationInDays = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,value: freezed == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as int?,expiresAt: freezed == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime?,expirationInDays: freezed == expirationInDays ? _self.expirationInDays : expirationInDays // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc

@JsonSerializable(includeIfNull: false)
class _UserMoneyGem implements UserMoneyGem {
   _UserMoneyGem({this.id, this.value, @JsonKey(toJson: _dateTimeToJson, fromJson: _dateTimeFromJson) this.expiresAt, this.expirationInDays});
  factory _UserMoneyGem.fromJson(Map<String, dynamic> json) => _$UserMoneyGemFromJson(json);

@override final  String? id;
@override final  int? value;
@override@JsonKey(toJson: _dateTimeToJson, fromJson: _dateTimeFromJson) final  DateTime? expiresAt;
@override final  int? expirationInDays;

/// Create a copy of UserMoneyGem
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserMoneyGemCopyWith<_UserMoneyGem> get copyWith => __$UserMoneyGemCopyWithImpl<_UserMoneyGem>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserMoneyGemToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserMoneyGem&&(identical(other.id, id) || other.id == id)&&(identical(other.value, value) || other.value == value)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&(identical(other.expirationInDays, expirationInDays) || other.expirationInDays == expirationInDays));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,value,expiresAt,expirationInDays);

@override
String toString() {
  return 'UserMoneyGem(id: $id, value: $value, expiresAt: $expiresAt, expirationInDays: $expirationInDays)';
}


}

/// @nodoc
abstract mixin class _$UserMoneyGemCopyWith<$Res> implements $UserMoneyGemCopyWith<$Res> {
  factory _$UserMoneyGemCopyWith(_UserMoneyGem value, $Res Function(_UserMoneyGem) _then) = __$UserMoneyGemCopyWithImpl;
@override @useResult
$Res call({
 String? id, int? value,@JsonKey(toJson: _dateTimeToJson, fromJson: _dateTimeFromJson) DateTime? expiresAt, int? expirationInDays
});




}
/// @nodoc
class __$UserMoneyGemCopyWithImpl<$Res>
    implements _$UserMoneyGemCopyWith<$Res> {
  __$UserMoneyGemCopyWithImpl(this._self, this._then);

  final _UserMoneyGem _self;
  final $Res Function(_UserMoneyGem) _then;

/// Create a copy of UserMoneyGem
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? value = freezed,Object? expiresAt = freezed,Object? expirationInDays = freezed,}) {
  return _then(_UserMoneyGem(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,value: freezed == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as int?,expiresAt: freezed == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime?,expirationInDays: freezed == expirationInDays ? _self.expirationInDays : expirationInDays // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}


/// @nodoc
mixin _$MoneyHistory {

 String? get id; String? get userId; int? get coins; List<UserMoneyGem>? get gems; String? get description; DateTime? get timestamp;
/// Create a copy of MoneyHistory
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MoneyHistoryCopyWith<MoneyHistory> get copyWith => _$MoneyHistoryCopyWithImpl<MoneyHistory>(this as MoneyHistory, _$identity);

  /// Serializes this MoneyHistory to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MoneyHistory&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.coins, coins) || other.coins == coins)&&const DeepCollectionEquality().equals(other.gems, gems)&&(identical(other.description, description) || other.description == description)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,coins,const DeepCollectionEquality().hash(gems),description,timestamp);

@override
String toString() {
  return 'MoneyHistory(id: $id, userId: $userId, coins: $coins, gems: $gems, description: $description, timestamp: $timestamp)';
}


}

/// @nodoc
abstract mixin class $MoneyHistoryCopyWith<$Res>  {
  factory $MoneyHistoryCopyWith(MoneyHistory value, $Res Function(MoneyHistory) _then) = _$MoneyHistoryCopyWithImpl;
@useResult
$Res call({
 String? id, String? userId, int? coins, List<UserMoneyGem>? gems, String? description, DateTime? timestamp
});




}
/// @nodoc
class _$MoneyHistoryCopyWithImpl<$Res>
    implements $MoneyHistoryCopyWith<$Res> {
  _$MoneyHistoryCopyWithImpl(this._self, this._then);

  final MoneyHistory _self;
  final $Res Function(MoneyHistory) _then;

/// Create a copy of MoneyHistory
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? userId = freezed,Object? coins = freezed,Object? gems = freezed,Object? description = freezed,Object? timestamp = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String?,coins: freezed == coins ? _self.coins : coins // ignore: cast_nullable_to_non_nullable
as int?,gems: freezed == gems ? _self.gems : gems // ignore: cast_nullable_to_non_nullable
as List<UserMoneyGem>?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,timestamp: freezed == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// @nodoc

@JsonSerializable(includeIfNull: false)
class _MoneyHistory implements MoneyHistory {
   _MoneyHistory({this.id, this.userId, this.coins, final  List<UserMoneyGem>? gems, this.description, this.timestamp}): _gems = gems;
  factory _MoneyHistory.fromJson(Map<String, dynamic> json) => _$MoneyHistoryFromJson(json);

@override final  String? id;
@override final  String? userId;
@override final  int? coins;
 final  List<UserMoneyGem>? _gems;
@override List<UserMoneyGem>? get gems {
  final value = _gems;
  if (value == null) return null;
  if (_gems is EqualUnmodifiableListView) return _gems;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  String? description;
@override final  DateTime? timestamp;

/// Create a copy of MoneyHistory
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MoneyHistoryCopyWith<_MoneyHistory> get copyWith => __$MoneyHistoryCopyWithImpl<_MoneyHistory>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MoneyHistoryToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MoneyHistory&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.coins, coins) || other.coins == coins)&&const DeepCollectionEquality().equals(other._gems, _gems)&&(identical(other.description, description) || other.description == description)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,coins,const DeepCollectionEquality().hash(_gems),description,timestamp);

@override
String toString() {
  return 'MoneyHistory(id: $id, userId: $userId, coins: $coins, gems: $gems, description: $description, timestamp: $timestamp)';
}


}

/// @nodoc
abstract mixin class _$MoneyHistoryCopyWith<$Res> implements $MoneyHistoryCopyWith<$Res> {
  factory _$MoneyHistoryCopyWith(_MoneyHistory value, $Res Function(_MoneyHistory) _then) = __$MoneyHistoryCopyWithImpl;
@override @useResult
$Res call({
 String? id, String? userId, int? coins, List<UserMoneyGem>? gems, String? description, DateTime? timestamp
});




}
/// @nodoc
class __$MoneyHistoryCopyWithImpl<$Res>
    implements _$MoneyHistoryCopyWith<$Res> {
  __$MoneyHistoryCopyWithImpl(this._self, this._then);

  final _MoneyHistory _self;
  final $Res Function(_MoneyHistory) _then;

/// Create a copy of MoneyHistory
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? userId = freezed,Object? coins = freezed,Object? gems = freezed,Object? description = freezed,Object? timestamp = freezed,}) {
  return _then(_MoneyHistory(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String?,coins: freezed == coins ? _self.coins : coins // ignore: cast_nullable_to_non_nullable
as int?,gems: freezed == gems ? _self._gems : gems // ignore: cast_nullable_to_non_nullable
as List<UserMoneyGem>?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,timestamp: freezed == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
