import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';
import 'package:tony_clou_app/shared/data/models/pagination.dart';

part 'index.freezed.dart';
part 'index.g.dart';

@freezed
abstract class GetDevicesOutput with _$GetDevicesOutput {
  @JsonSerializable(includeIfNull: false)
  factory GetDevicesOutput({List<String>? devices}) = _GetDevicesOutput;

  factory GetDevicesOutput.fromJson(Map<String, dynamic> json) =>
      _$GetDevicesOutputFromJson(json);
}

@freezed
abstract class GetUserOutput with _$GetUserOutput {
  @JsonSerializable(includeIfNull: false)
  factory GetUserOutput({User? user}) = _GetUserOutput;

  factory GetUserOutput.fromJson(Map<String, dynamic> json) =>
      _$GetUserOutputFromJson(json);
}

@freezed
abstract class GetUsersInput with _$GetUsersInput {
  @JsonSerializable(includeIfNull: false)
  factory GetUsersInput({UserFilter? filter, Pagination? pagination}) =
      _GetUsersInput;

  factory GetUsersInput.fromJson(Map<String, dynamic> json) =>
      _$GetUsersInputFromJson(json);
}

@freezed
abstract class GetUsersOutput with _$GetUsersOutput {
  @JsonSerializable(includeIfNull: false)
  factory GetUsersOutput({List<User>? data, int? totalItems}) = _GetUsersOutput;

  factory GetUsersOutput.fromJson(Map<String, dynamic> json) =>
      _$GetUsersOutputFromJson(json);
}

@freezed
abstract class GetMoneyHistoryInput with _$GetMoneyHistoryInput {
  @JsonSerializable(includeIfNull: false)
  factory GetMoneyHistoryInput({String? userId, Pagination? pagination}) =
      _GetMoneyHistoryInput;

  factory GetMoneyHistoryInput.fromJson(Map<String, dynamic> json) =>
      _$GetMoneyHistoryInputFromJson(json);
}

@freezed
abstract class GetMoneyHistoryOutput with _$GetMoneyHistoryOutput {
  factory GetMoneyHistoryOutput({List<MoneyHistory>? items, int? totalItems}) =
      _GetMoneyHistoryOutput;

  factory GetMoneyHistoryOutput.fromJson(Map<String, dynamic> json) =>
      _$GetMoneyHistoryOutputFromJson(json);
}
