// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'index.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_GetDevicesOutput _$GetDevicesOutputFromJson(Map<String, dynamic> json) =>
    _GetDevicesOutput(
      devices:
          (json['devices'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$GetDevicesOutputToJson(_GetDevicesOutput instance) =>
    <String, dynamic>{if (instance.devices case final value?) 'devices': value};

_GetUserOutput _$GetUserOutputFromJson(Map<String, dynamic> json) =>
    _GetUserOutput(
      user:
          json['user'] == null
              ? null
              : User.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$GetUserOutputToJson(_GetUserOutput instance) =>
    <String, dynamic>{if (instance.user case final value?) 'user': value};

_GetUsersInput _$GetUsersInputFromJson(Map<String, dynamic> json) =>
    _GetUsersInput(
      filter:
          json['filter'] == null
              ? null
              : UserFilter.fromJson(json['filter'] as Map<String, dynamic>),
      pagination:
          json['pagination'] == null
              ? null
              : Pagination.fromJson(json['pagination'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$GetUsersInputToJson(_GetUsersInput instance) =>
    <String, dynamic>{
      if (instance.filter case final value?) 'filter': value,
      if (instance.pagination case final value?) 'pagination': value,
    };

_GetUsersOutput _$GetUsersOutputFromJson(Map<String, dynamic> json) =>
    _GetUsersOutput(
      data:
          (json['data'] as List<dynamic>?)
              ?.map((e) => User.fromJson(e as Map<String, dynamic>))
              .toList(),
      totalItems: (json['totalItems'] as num?)?.toInt(),
    );

Map<String, dynamic> _$GetUsersOutputToJson(_GetUsersOutput instance) =>
    <String, dynamic>{
      if (instance.data case final value?) 'data': value,
      if (instance.totalItems case final value?) 'totalItems': value,
    };

_GetMoneyHistoryInput _$GetMoneyHistoryInputFromJson(
  Map<String, dynamic> json,
) => _GetMoneyHistoryInput(
  userId: json['userId'] as String?,
  pagination:
      json['pagination'] == null
          ? null
          : Pagination.fromJson(json['pagination'] as Map<String, dynamic>),
);

Map<String, dynamic> _$GetMoneyHistoryInputToJson(
  _GetMoneyHistoryInput instance,
) => <String, dynamic>{
  if (instance.userId case final value?) 'userId': value,
  if (instance.pagination case final value?) 'pagination': value,
};

_GetMoneyHistoryOutput _$GetMoneyHistoryOutputFromJson(
  Map<String, dynamic> json,
) => _GetMoneyHistoryOutput(
  items:
      (json['items'] as List<dynamic>?)
          ?.map((e) => MoneyHistory.fromJson(e as Map<String, dynamic>))
          .toList(),
  totalItems: (json['totalItems'] as num?)?.toInt(),
);

Map<String, dynamic> _$GetMoneyHistoryOutputToJson(
  _GetMoneyHistoryOutput instance,
) => <String, dynamic>{
  'items': instance.items,
  'totalItems': instance.totalItems,
};
