import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:tony_clou_app/core/navigation/index.gr.dart';
import 'package:tony_clou_app/features/authorization/data/repository/index.dart';
import 'package:tony_clou_app/shared/widgets/interactive/elevated_button.dart';

/// Logout button widget
class UserLogoutButton extends StatelessWidget {
  const UserLogoutButton({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomElevatedButton(
      onPressed: () async {
        await AuthRepository.logout();
        if (context.mounted) {
          context.router.replace(const SplashRoute());
        }
      },
      text: 'Выйти из аккаунта',
    );
  }
}
