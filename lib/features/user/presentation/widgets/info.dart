import 'package:flutter/cupertino.dart';
import 'package:intl/intl.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';

/// User info list widget
class UserInfoList extends StatelessWidget {
  const UserInfoList({
    super.key,
    required this.user,
    required this.descriptionColor,
  });

  final User user;
  final Color descriptionColor;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _UserInfoRow(
          label: 'Дата рождения',
          value: DateFormat(
            'dd.MM.yyyy',
          ).format(user.dateOfBirth ?? DateTime(1900)),
          descriptionColor: descriptionColor,
        ),
        _UserInfoRow(
          label: 'Номер телефона',
          value: user.telephoneNumber ?? '?',
          descriptionColor: descriptionColor,
        ),
        _UserInfoRow(
          label: 'Почта',
          value: user.email ?? '?',
          descriptionColor: descriptionColor,
        ),
        _UserInfoRow(
          label: 'Дата создания',
          value: DateFormat(
            'dd.MM.yyyy',
          ).format(user.createdAt ?? DateTime(1900)),
          descriptionColor: descriptionColor,
        ),
      ],
    );
  }
}

/// User info row widget
class _UserInfoRow extends StatelessWidget {
  const _UserInfoRow({
    required this.label,
    required this.value,
    required this.descriptionColor,
  });

  final String label;
  final String value;
  final Color descriptionColor;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Fonts.bodyMedium.merge(TextStyle(color: descriptionColor)),
        ),
        Text(value, style: Fonts.bodyMedium),
      ],
    );
  }
}
