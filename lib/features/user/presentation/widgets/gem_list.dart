import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/interactive/ghost_button.dart';

/// Gem list widget
class UserGemList extends StatelessWidget {
  const UserGemList({
    super.key,
    required this.controllers,
    required this.expirationDates,
    required this.minimumDate,
    required this.onDateChanged,
  });

  final List<TextEditingController> controllers;
  final List<DateTime> expirationDates;
  final DateTime minimumDate;
  final void Function(int, DateTime) onDateChanged;

  @override
  Widget build(BuildContext context) {
    if (controllers.isEmpty) {
      return Text(
        'Gems отсутствуют',
        style: Fonts.labelSmall,
        textAlign: TextAlign.center,
      );
    }

    return ListView.separated(
      itemCount: controllers.length,
      itemBuilder:
          (context, index) => _GemItem(
            index: index,
            controller: controllers[index],
            expirationDate: expirationDates[index],
            minimumDate: minimumDate,
            onDateChanged: (date) => onDateChanged(index, date),
          ),
      separatorBuilder: (_, _) => SizedBox(height: 8.0),
    );
  }
}

/// Individual gem item
class _GemItem extends StatelessWidget {
  const _GemItem({
    required this.index,
    required this.controller,
    required this.expirationDate,
    required this.minimumDate,
    required this.onDateChanged,
  });

  final int index;
  final TextEditingController controller;
  final DateTime expirationDate;
  final DateTime minimumDate;
  final void Function(DateTime) onDateChanged;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // Text('Gem ${index + 1}', style: Fonts.labelMedium),
        // const SizedBox(height: 8.0),
        Expanded(
          child: TextField(
            // do compact
            decoration: const InputDecoration(
              hintText: '0',
              contentPadding: EdgeInsets.symmetric(
                horizontal: 12.0,
                vertical: 4.0,
              ),
            ),
            controller: controller,
            style: Fonts.labelSmall,
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          ),
        ),
        const SizedBox(width: 4.0),
        GhostButton(
          onTap: () => _showDatePicker(context),
          child: Text(
            'До: ${DateFormat('dd.MM.yyyy').format(expirationDate.toLocal())}',
            style: Fonts.bodySmall.merge(
              TextStyle(color: AppColors.darkSecondary),
            ),
          ),
        ),
      ],
    );
  }

  void _showDatePicker(BuildContext context) {
    var tempDate = expirationDate;
    showCupertinoModalPopup(
      context: context,
      builder:
          (context) => Container(
            height: 300.0,
            color: CupertinoColors.systemBackground.resolveFrom(context),
            child: CupertinoDatePicker(
              mode: CupertinoDatePickerMode.date,
              initialDateTime: tempDate,
              minimumDate: minimumDate,
              maximumDate: DateTime(2100),
              onDateTimeChanged: (date) => onDateChanged(date.toUtc()),
            ),
          ),
    );
  }
}
