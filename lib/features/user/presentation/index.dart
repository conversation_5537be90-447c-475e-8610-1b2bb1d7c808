import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:tony_clou_app/core/gen/assets.gen.dart';
import 'package:tony_clou_app/core/navigation/index.gr.dart';
import 'package:tony_clou_app/features/main/presentation/bloc/user/index.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';
import 'package:tony_clou_app/features/user/data/repository/index.dart';
import 'package:tony_clou_app/features/user/presentation/widgets/gem_list.dart';
import 'package:tony_clou_app/features/user/presentation/widgets/info.dart';
import 'package:tony_clou_app/features/user/presentation/widgets/logout.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/card/index.dart';
import 'package:tony_clou_app/shared/widgets/containers/wrapper/index.dart';
import 'package:tony_clou_app/shared/widgets/interactive/elevated_button.dart';
import 'package:tony_clou_app/shared/widgets/utility/snackbar.dart';

/// User profile screen
@RoutePage()
class UserScreen extends StatefulWidget {
  const UserScreen({super.key, this.userId});

  final String? userId;

  @override
  State<UserScreen> createState() => _UserScreenState();
}

class _UserScreenState extends State<UserScreen> {
  final _coinController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _fetchUserData();
  }

  @override
  void dispose() {
    _coinController.dispose();
    super.dispose();
  }

  /// Fetches user data based on userId presence
  Future<void> _fetchUserData() async {
    if (!mounted) return;

    try {
      if (widget.userId != null) {
        final user = (await UserRepository.getUser(widget.userId!)).data?.user;
        if (user != null && mounted) {
          context.read<BlocUser>().add(SetUser(user));
        }
      } else {
        final user = (await UserRepository.getSelf()).data?.user;
        if (!mounted) return;
        if (user != null) {
          context.read<BlocUser>().add(SetSelf(user));
        } else {
          context.router.replace(const AuthorizationRoute());
        }
      }

      await Future.delayed(const Duration(seconds: 1));
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Ошибка при загрузке данных пользователя');
      }
    }
  }

  /// Shows error snackbar with given message
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(CustomSnackbar(text: message).toSnackBar(context));
  }

  @override
  Widget build(BuildContext context) {
    return Wrapper(
      appBar: CupertinoNavigationBar(
        automaticallyImplyLeading: true,
        previousPageTitle: '',
        middle: Text('Профиль', style: Fonts.labelMedium),
      ),
      body: RefreshIndicator.adaptive(
        onRefresh: _fetchUserData,
        child: _UserProfileContent(
          userId: widget.userId,
          coinController: _coinController,
          onFetchUser: _fetchUserData,
        ),
      ),
      extendBody: true,
      withSafeArea: false,
    );
  }
}

/// User profile content widget
class _UserProfileContent extends StatelessWidget {
  const _UserProfileContent({
    required this.userId,
    required this.coinController,
    required this.onFetchUser,
  });

  final String? userId;
  final TextEditingController coinController;
  final Future<void> Function() onFetchUser;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final coinStyle = Fonts.labelMedium.merge(
      TextStyle(color: isDarkTheme ? AppColors.darkCoin : AppColors.lightCoin),
    );
    final gemStyle = Fonts.labelMedium.merge(
      TextStyle(color: isDarkTheme ? AppColors.darkGem : AppColors.lightGem),
    );
    final descriptionColor =
        isDarkTheme ? AppColors.darkDescription : AppColors.lightDescription;

    return BlocBuilder<BlocUser, BlocUserState>(
      builder: (context, state) {
        final user = userId != null ? state.user : state.self;
        final gems = BlocUser.getCompressionGem(user);

        return ListView(
          padding: const EdgeInsets.all(12.0),
          children: [
            // avatar and name
            Column(
              children: [
                Assets.icons.userAvatar.svg(width: 72.0),
                const SizedBox(height: 16.0),
                Text(
                  '${user.surname ?? ''} ${user.name ?? ''}'.trim(),
                  style: Fonts.labelLarge,
                  textAlign: TextAlign.center,
                ),
              ],
            ),

            // role
            if (user.role != null)
              Text(
                user.role!.getName(),
                style: Fonts.bodySmall,
                textAlign: TextAlign.center,
              ),

            // coins
            const SizedBox(height: 32.0),
            _CurrencyCard(
              title: 'Tony Clou Coins',
              value: user.money?.coins ?? 0,
              style: coinStyle,
              icon: Assets.icons.coin.svg(width: 48.0),
              onTap:
                  state.self.role == UserRole.admin
                      ? () => _showCoinEditor(context, user, coinController)
                      : null,
            ),

            // gems
            const SizedBox(height: 12.0),
            _CurrencyCard(
              title: 'Tony Clou Gems',
              value: gems.value ?? 0,
              style: gemStyle,
              description:
                  gems.expiresAt != null
                      ? 'До: ${DateFormat('dd.MM.yyyy, HH:mm').format(gems.expiresAt!.toLocal())}'
                      : null,
              descriptionColor: descriptionColor,
              icon: Assets.icons.gem.svg(width: 48.0),
              onTap:
                  state.self.role == UserRole.admin
                      ? () => _showGemEditor(context, user, onFetchUser)
                      : null,
            ),

            // user info
            const SizedBox(height: 32.0),
            UserInfoList(user: user, descriptionColor: descriptionColor),

            // and logout
            if (userId == null) ...[
              const SizedBox(height: 32.0),
              UserLogoutButton(),
            ],

            // info about money
            SizedBox(height: 32.0),
            _MoneyInfo(),
            SizedBox(height: 64.0),
          ],
        );
      },
    );
  }

  /// Shows coin editor modal
  void _showCoinEditor(
    BuildContext context,
    User user,
    TextEditingController controller,
  ) {
    controller.text = (user.money?.coins ?? 0).toString();
    // final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    showModalBottomSheet(
      context: context,
      showDragHandle: true,
      constraints: BoxConstraints(maxHeight: 800.0),
      // backgroundColor: Theme.of(context).colorScheme.surface,
      // shape: RoundedRectangleBorder(
      //   side: BorderSide(
      //     color: isDarkTheme ? AppColors.darkStroke : AppColors.lightStroke,
      //     width: 1.0,
      //   ),
      //   borderRadius: BorderRadius.vertical(top: Radius.circular(32.0)),
      // ),
      isScrollControlled: true,
      builder:
          (context) => _CoinEditor(
            controller: controller,
            user: user,
            onSave: () async {
              await UserRepository.edit(
                user.copyWith(
                  money: UserMoney(
                    coins: int.tryParse(controller.text) ?? 0,
                    gems: user.money?.gems,
                  ),
                ),
              );
              if (context.mounted) {
                await context.maybePop();
                await onFetchUser();
              }
            },
          ),
    );
  }

  /// Shows gem editor modal
  void _showGemEditor(
    BuildContext context,
    User user,
    Future<void> Function() onFetchUser,
  ) {
    // final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    showModalBottomSheet(
      context: context,
      showDragHandle: true,
      constraints: BoxConstraints(maxHeight: 800.0),
      // backgroundColor: Theme.of(context).colorScheme.surface,
      // shape: RoundedRectangleBorder(
      //   side: BorderSide(
      //     color: isDarkTheme ? AppColors.darkStroke : AppColors.lightStroke,
      //     width: 1.0,
      //   ),
      //   borderRadius: BorderRadius.vertical(top: Radius.circular(32.0)),
      // ),
      // useNestedNavigation: true,
      isScrollControlled: true,
      builder: (context) => _GemEditor(user: user, onSave: onFetchUser),
    );
  }
}

/// Currency card widget for coins and gems
class _CurrencyCard extends StatelessWidget {
  const _CurrencyCard({
    required this.title,
    required this.value,
    required this.style,
    required this.icon,
    this.description,
    this.descriptionColor,
    this.onTap,
  });

  final String title;
  final int value;
  final TextStyle style;
  final Widget icon;
  final String? description;
  final Color? descriptionColor;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      onTap: onTap,
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: style),
                if (description != null)
                  Text(
                    description!,
                    style: Fonts.bodySmall.merge(
                      TextStyle(color: descriptionColor),
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(width: 16.0),
          Text('$value', style: style),
          const SizedBox(width: 12.0),
          icon,
        ],
      ),
    );
  }
}

/// Coin editor modal
class _CoinEditor extends StatelessWidget {
  const _CoinEditor({
    required this.controller,
    required this.user,
    required this.onSave,
  });

  final TextEditingController controller;
  final User user;
  final Future<void> Function() onSave;

  @override
  Widget build(BuildContext context) {
    return Wrapper(
      body: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('Изменение Tony Clou Coins', style: Fonts.titleSmall),
            const SizedBox(height: 12.0),
            TextField(
              decoration: const InputDecoration(hintText: '0'),
              controller: controller,
              style: Fonts.labelSmall,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            ),
            const SizedBox(height: 32.0),
            CustomElevatedButton(text: 'Сохранить', onPressed: onSave),
          ],
        ),
      ),
    );
  }
}

/// Gem editor modal
class _GemEditor extends StatefulWidget {
  const _GemEditor({required this.user, required this.onSave});

  final User user;
  final Future<void> Function() onSave;

  @override
  State<_GemEditor> createState() => _GemEditorState();
}

class _GemEditorState extends State<_GemEditor> {
  late List<TextEditingController> _gemControllers;
  late List<DateTime> _expirationDates;
  final _now = DateTime.now().toUtc();
  late final _minimumDate = DateTime(_now.year, _now.month, _now.day);

  @override
  void initState() {
    super.initState();
    final gems = widget.user.money?.gems ?? [];
    _gemControllers =
        gems
            .map(
              (gem) =>
                  TextEditingController(text: gem.value?.toString() ?? '0'),
            )
            .toList();
    _expirationDates =
        gems.map((gem) {
          final expiresAt = gem.expiresAt?.toUtc() ?? _now;
          return expiresAt.isAfter(_minimumDate) ||
                  expiresAt.isAtSameMomentAs(_minimumDate)
              ? expiresAt
              : _minimumDate;
        }).toList();
  }

  @override
  void dispose() {
    for (var controller in _gemControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Wrapper(
      body: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('Изменение Tony Clou Gems', style: Fonts.titleSmall),
            const SizedBox(height: 12.0),
            Expanded(
              child: UserGemList(
                controllers: _gemControllers,
                expirationDates: _expirationDates,
                minimumDate: _minimumDate,
                onDateChanged: (index, date) {
                  setState(() => _expirationDates[index] = date);
                },
              ),
            ),
            CustomElevatedButton(
              text: 'Добавить Gem',
              onPressed: () {
                setState(() {
                  _gemControllers.add(TextEditingController(text: '0'));
                  _expirationDates.add(_minimumDate);
                });
              },
            ),
            const SizedBox(height: 12.0),
            CustomElevatedButton(
              text: 'Сохранить',
              onPressed: () async {
                final updatedGems = List.generate(
                  _gemControllers.length,
                  (index) => UserMoneyGem(
                    id:
                        index < (widget.user.money?.gems?.length ?? 0)
                            ? widget.user.money!.gems![index].id
                            : null,
                    value: int.tryParse(_gemControllers[index].text) ?? 0,
                    expiresAt: _expirationDates[index],
                    expirationInDays:
                        index < (widget.user.money?.gems?.length ?? 0)
                            ? widget.user.money!.gems![index].expirationInDays
                            : null,
                  ),
                );
                await UserRepository.edit(
                  widget.user.copyWith(
                    money: UserMoney(
                      coins: widget.user.money?.coins ?? 0,
                      gems: updatedGems,
                    ),
                  ),
                );
                if (context.mounted) {
                  await context.maybePop();
                  await widget.onSave();
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}

class _MoneyInfo extends StatelessWidget {
  const _MoneyInfo();

  @override
  Widget build(BuildContext context) {
    final textColor = CupertinoColors.secondaryLabel.resolveFrom(context);

    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: CupertinoColors.systemGrey6.resolveFrom(context),
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Coins & Gems',
            style: Fonts.labelSmall.copyWith(
              color: CupertinoColors.secondaryLabel.resolveFrom(context),
            ),
          ),
          SizedBox(height: 16.0),
          Text(
            '''С чего начисляются Coins:
– С покупок одежды
– Со съемок и показов
– С любых услуг оказываемые TONY CLOU PRODUCTION 
– C любых активностей связанные с TONY CLOU PRODUCTION. Даже если бесплатные мероприятия.''',
            style: Fonts.bodySmall.copyWith(color: textColor),
          ),
          SizedBox(height: 8.0),
          Text(
            '''На что можно потратить Coins:
– На одежду до 50%
– На аксессуары до 25%
– До 10% на различные иные продукты TONY CLOU PRODUCTION. Это могут: съёмки, мероприятия, покупка билетов, фотопозирование и дефиле, а так различные туры и выездные вечеринки.''',
            style: Fonts.bodySmall.copyWith(color: textColor),
          ),
          SizedBox(height: 16.0),
          Text(
            'Gems можно получить за участие в проектах а также при накоплении каждых 100 000 Сoins 5% из них уходят в gems (5000)',
            style: Fonts.bodySmall.copyWith(color: textColor),
          ),
          SizedBox(height: 8.0),
          Text(
            'Gems используются до 100% на оплату одежды',
            style: Fonts.bodySmall.copyWith(color: textColor),
          ),
        ],
      ),
    );
  }
}
