import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:tony_clou_app/core/gen/assets.gen.dart';
import 'package:tony_clou_app/core/navigation/index.gr.dart';
import 'package:tony_clou_app/features/main/presentation/bloc/user/index.dart';
import 'package:tony_clou_app/features/user/data/models/user.dart';
import 'package:tony_clou_app/shared/styles/colors.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/card/index.dart';

class UserCard extends StatelessWidget {
  const UserCard({super.key, required this.data});

  final User data;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final gems = BlocUser.getCompressionGem(data);

    return CustomCard(
      onTap: () {
        context.router.push(UserRoute(userId: data.id));
      },
      child: Row(
        children: [
          Assets.icons.userAvatar.svg(width: 42),
          SizedBox(width: 8.0),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('${data.surname} ${data.name}', style: Fonts.labelSmall),
                Text('${data.telephoneNumber}', style: Fonts.bodySmall),
                Wrap(
                  spacing: 8.0,
                  runSpacing: 8.0,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      spacing: 4.0,
                      children: [
                        Assets.icons.coin.svg(width: 20.0),
                        Text(
                          '${data.money?.coins ?? 0}',
                          style: Fonts.bodySmall.merge(
                            TextStyle(
                              color:
                                  isDarkTheme
                                      ? AppColors.darkCoin
                                      : AppColors.lightCoin,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      spacing: 4.0,
                      children: [
                        Assets.icons.gem.svg(width: 20.0),
                        Text(
                          '${gems.value ?? 0}',
                          style: Fonts.bodySmall.merge(
                            TextStyle(
                              color:
                                  isDarkTheme
                                      ? AppColors.darkGem
                                      : AppColors.lightGem,
                            ),
                          ),
                        ),
                        if (gems.expiresAt != null)
                          SizedBox(width: 12.0, child: Divider()),
                        if (gems.expiresAt != null)
                          Text(
                            '${gems.expiresAt!.difference(DateTime.now()).inHours} ч.',
                            style: Fonts.bodySmall.merge(
                              TextStyle(
                                // fontSize: 10.0,
                                color:
                                    isDarkTheme
                                        ? AppColors.darkDescription
                                        : AppColors.lightDescription,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
