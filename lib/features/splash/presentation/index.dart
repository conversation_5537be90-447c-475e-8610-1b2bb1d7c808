import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:tony_clou_app/core/gen/assets.gen.dart';
import 'package:tony_clou_app/core/navigation/index.gr.dart';
import 'package:tony_clou_app/features/authorization/data/repository/index.dart';
import 'package:tony_clou_app/features/main/presentation/bloc/user/index.dart';
import 'package:tony_clou_app/features/user/data/repository/index.dart';
import 'package:tony_clou_app/shared/styles/fonts.dart';
import 'package:tony_clou_app/shared/widgets/containers/wrapper/index.dart';
import 'package:tony_clou_app/shared/widgets/interactive/ghost_button.dart';

@RoutePage()
class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    enterInApp(context);

    return Wrapper(
      body: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            GhostButton(
              onTap: () => context.router.push(AuthorizationRoute()),
              child: Hero(
                tag: 'app_title',
                transitionOnUserGestures: true,
                child: Material(
                  type: MaterialType.transparency,
                  child: Text(
                    'Tony Clou App',
                    style: Fonts.headlineLarge,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
            SizedBox(height: 12.0),

            Padding(
              padding: const EdgeInsets.all(12.0),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12.0),
                child: Assets.images.poster.image(),
              ),
            ),

            SizedBox(height: 8.0),
            CupertinoActivityIndicator(),
            // Text(
            //   'Loading...',
            //   style: Fonts.bodySmall,
            //   textAlign: TextAlign.center,
            // ),
          ],
        ),
      ),
    );
  }

  void enterInApp(BuildContext context) async {
    final prefs = FlutterSecureStorage();
    final accessToken = await prefs.read(key: 'accessToken');
    final refreshToken = await prefs.read(key: 'refreshToken');
    final deviceId = await prefs.read(key: 'deviceId');

    // await Future.delayed(Duration(seconds: 2));

    Future<void> getUserAndRedirect() async {
      // get user
      final resU = await UserRepository.getSelf();
      final user = resU.data?.user;

      // set user and redirect
      if (user != null) {
        context.read<BlocUser>().add(SetSelf(user));
        context.router.replace(MainRoute());
      } else {
        context.router.replace(AuthorizationRoute());
      }
    }

    if (accessToken != null) {
      // get user and redirect
      await getUserAndRedirect();
    } else if (refreshToken != null && deviceId != null) {
      // refresh token
      final resR = await AuthRepository.refreshToken(
        refreshToken: refreshToken,
        deviceId: deviceId,
      );
      if (resR.data?.accessToken != null && resR.data?.refreshToken != null) {
        prefs.write(key: 'accessToken', value: resR.data?.accessToken);
        prefs.write(key: 'refreshToken', value: resR.data?.refreshToken);
      }

      // get user and redirect
      await getUserAndRedirect();
    } else {
      context.router.replace(AuthorizationRoute());
    }
  }
}
