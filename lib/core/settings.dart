import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CoreSettings {
  // Цвета оверлея системы
  void getSystemUIOverlaySettings(bool isDarkTheme) {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        // top
        statusBarColor: Colors.transparent,
        statusBarIconBrightness:
            isDarkTheme ? Brightness.light : Brightness.dark,

        // bottom
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness:
            isDarkTheme ? Brightness.light : Brightness.dark,
        // systemNavigationBarColor:
        //     isDarkTheme ? AppColors.darkBackground : AppColors.lightBackground,
      ),
    );
  }

  // Ориентация экрана
  void getPreferredOrientationsSettings() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }
}
