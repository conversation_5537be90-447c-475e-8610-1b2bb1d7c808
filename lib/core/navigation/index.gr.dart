// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i20;
import 'package:flutter/cupertino.dart' as _i21;
import 'package:tony_clou_app/features/about/presentation/index.dart' as _i1;
import 'package:tony_clou_app/features/admin/presentation/checks_list.dart'
    as _i7;
import 'package:tony_clou_app/features/admin/presentation/generated.dart'
    as _i9;
import 'package:tony_clou_app/features/admin/presentation/index.dart' as _i3;
import 'package:tony_clou_app/features/admin/presentation/user_list.dart'
    as _i17;
import 'package:tony_clou_app/features/authorization/presentation/alternative.dart'
    as _i4;
import 'package:tony_clou_app/features/authorization/presentation/index.dart'
    as _i5;
import 'package:tony_clou_app/features/authorization/presentation/verify_code.dart'
    as _i19;
import 'package:tony_clou_app/features/balance/presentation/index.dart' as _i6;
import 'package:tony_clou_app/features/check/presentation/index.dart' as _i2;
import 'package:tony_clou_app/features/contacts/presentation/index.dart' as _i8;
import 'package:tony_clou_app/features/locations/presentation/index.dart'
    as _i10;
import 'package:tony_clou_app/features/main/presentation/index.dart' as _i11;
import 'package:tony_clou_app/features/qr/data/models/qr.dart' as _i22;
import 'package:tony_clou_app/features/qr/presentation/index.dart' as _i15;
import 'package:tony_clou_app/features/registration/presentation/index.dart'
    as _i12;
import 'package:tony_clou_app/features/registration/presentation/step2.dart'
    as _i13;
import 'package:tony_clou_app/features/registration/presentation/step3.dart'
    as _i14;
import 'package:tony_clou_app/features/splash/presentation/index.dart' as _i16;
import 'package:tony_clou_app/features/user/presentation/index.dart' as _i18;

/// generated route for
/// [_i1.AboutScreen]
class AboutRoute extends _i20.PageRouteInfo<void> {
  const AboutRoute({List<_i20.PageRouteInfo>? children})
    : super(AboutRoute.name, initialChildren: children);

  static const String name = 'AboutRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i1.AboutScreen();
    },
  );
}

/// generated route for
/// [_i2.AddCheckScreen]
class AddCheckRoute extends _i20.PageRouteInfo<void> {
  const AddCheckRoute({List<_i20.PageRouteInfo>? children})
    : super(AddCheckRoute.name, initialChildren: children);

  static const String name = 'AddCheckRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i2.AddCheckScreen();
    },
  );
}

/// generated route for
/// [_i3.AdminScreen]
class AdminRoute extends _i20.PageRouteInfo<void> {
  const AdminRoute({List<_i20.PageRouteInfo>? children})
    : super(AdminRoute.name, initialChildren: children);

  static const String name = 'AdminRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i3.AdminScreen();
    },
  );
}

/// generated route for
/// [_i4.AlternativeSignInScreen]
class AlternativeSignInRoute extends _i20.PageRouteInfo<void> {
  const AlternativeSignInRoute({List<_i20.PageRouteInfo>? children})
    : super(AlternativeSignInRoute.name, initialChildren: children);

  static const String name = 'AlternativeSignInRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i4.AlternativeSignInScreen();
    },
  );
}

/// generated route for
/// [_i5.AuthorizationScreen]
class AuthorizationRoute extends _i20.PageRouteInfo<void> {
  const AuthorizationRoute({List<_i20.PageRouteInfo>? children})
    : super(AuthorizationRoute.name, initialChildren: children);

  static const String name = 'AuthorizationRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i5.AuthorizationScreen();
    },
  );
}

/// generated route for
/// [_i6.BalanceScreen]
class BalanceRoute extends _i20.PageRouteInfo<void> {
  const BalanceRoute({List<_i20.PageRouteInfo>? children})
    : super(BalanceRoute.name, initialChildren: children);

  static const String name = 'BalanceRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i6.BalanceScreen();
    },
  );
}

/// generated route for
/// [_i7.ChecksListScreen]
class ChecksListRoute extends _i20.PageRouteInfo<void> {
  const ChecksListRoute({List<_i20.PageRouteInfo>? children})
    : super(ChecksListRoute.name, initialChildren: children);

  static const String name = 'ChecksListRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i7.ChecksListScreen();
    },
  );
}

/// generated route for
/// [_i8.ContactsScreen]
class ContactsRoute extends _i20.PageRouteInfo<void> {
  const ContactsRoute({List<_i20.PageRouteInfo>? children})
    : super(ContactsRoute.name, initialChildren: children);

  static const String name = 'ContactsRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i8.ContactsScreen();
    },
  );
}

/// generated route for
/// [_i9.GeneratedQRScreen]
class GeneratedQRRoute extends _i20.PageRouteInfo<GeneratedQRRouteArgs> {
  GeneratedQRRoute({
    _i21.Key? key,
    required String data,
    required _i22.QrCodeModel qrCode,
    List<_i20.PageRouteInfo>? children,
  }) : super(
         GeneratedQRRoute.name,
         args: GeneratedQRRouteArgs(key: key, data: data, qrCode: qrCode),
         initialChildren: children,
       );

  static const String name = 'GeneratedQRRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<GeneratedQRRouteArgs>();
      return _i9.GeneratedQRScreen(
        key: args.key,
        data: args.data,
        qrCode: args.qrCode,
      );
    },
  );
}

class GeneratedQRRouteArgs {
  const GeneratedQRRouteArgs({
    this.key,
    required this.data,
    required this.qrCode,
  });

  final _i21.Key? key;

  final String data;

  final _i22.QrCodeModel qrCode;

  @override
  String toString() {
    return 'GeneratedQRRouteArgs{key: $key, data: $data, qrCode: $qrCode}';
  }
}

/// generated route for
/// [_i10.LocationsScreen]
class LocationsRoute extends _i20.PageRouteInfo<void> {
  const LocationsRoute({List<_i20.PageRouteInfo>? children})
    : super(LocationsRoute.name, initialChildren: children);

  static const String name = 'LocationsRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i10.LocationsScreen();
    },
  );
}

/// generated route for
/// [_i11.MainScreen]
class MainRoute extends _i20.PageRouteInfo<void> {
  const MainRoute({List<_i20.PageRouteInfo>? children})
    : super(MainRoute.name, initialChildren: children);

  static const String name = 'MainRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i11.MainScreen();
    },
  );
}

/// generated route for
/// [_i12.RegistationScreen]
class RegistationRoute extends _i20.PageRouteInfo<void> {
  const RegistationRoute({List<_i20.PageRouteInfo>? children})
    : super(RegistationRoute.name, initialChildren: children);

  static const String name = 'RegistationRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i12.RegistationScreen();
    },
  );
}

/// generated route for
/// [_i13.RegistrationScreenStep2]
class RegistrationRouteStep2 extends _i20.PageRouteInfo<void> {
  const RegistrationRouteStep2({List<_i20.PageRouteInfo>? children})
    : super(RegistrationRouteStep2.name, initialChildren: children);

  static const String name = 'RegistrationRouteStep2';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i13.RegistrationScreenStep2();
    },
  );
}

/// generated route for
/// [_i14.RegistrationScreenStep3]
class RegistrationRouteStep3 extends _i20.PageRouteInfo<void> {
  const RegistrationRouteStep3({List<_i20.PageRouteInfo>? children})
    : super(RegistrationRouteStep3.name, initialChildren: children);

  static const String name = 'RegistrationRouteStep3';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i14.RegistrationScreenStep3();
    },
  );
}

/// generated route for
/// [_i15.ScanScreen]
class ScanRoute extends _i20.PageRouteInfo<void> {
  const ScanRoute({List<_i20.PageRouteInfo>? children})
    : super(ScanRoute.name, initialChildren: children);

  static const String name = 'ScanRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i15.ScanScreen();
    },
  );
}

/// generated route for
/// [_i16.SplashScreen]
class SplashRoute extends _i20.PageRouteInfo<void> {
  const SplashRoute({List<_i20.PageRouteInfo>? children})
    : super(SplashRoute.name, initialChildren: children);

  static const String name = 'SplashRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i16.SplashScreen();
    },
  );
}

/// generated route for
/// [_i17.UserListScreen]
class UserListRoute extends _i20.PageRouteInfo<void> {
  const UserListRoute({List<_i20.PageRouteInfo>? children})
    : super(UserListRoute.name, initialChildren: children);

  static const String name = 'UserListRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i17.UserListScreen();
    },
  );
}

/// generated route for
/// [_i18.UserScreen]
class UserRoute extends _i20.PageRouteInfo<UserRouteArgs> {
  UserRoute({_i21.Key? key, String? userId, List<_i20.PageRouteInfo>? children})
    : super(
        UserRoute.name,
        args: UserRouteArgs(key: key, userId: userId),
        initialChildren: children,
      );

  static const String name = 'UserRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<UserRouteArgs>(
        orElse: () => const UserRouteArgs(),
      );
      return _i18.UserScreen(key: args.key, userId: args.userId);
    },
  );
}

class UserRouteArgs {
  const UserRouteArgs({this.key, this.userId});

  final _i21.Key? key;

  final String? userId;

  @override
  String toString() {
    return 'UserRouteArgs{key: $key, userId: $userId}';
  }
}

/// generated route for
/// [_i19.VerifyCodeScreen]
class VerifyCodeRoute extends _i20.PageRouteInfo<void> {
  const VerifyCodeRoute({List<_i20.PageRouteInfo>? children})
    : super(VerifyCodeRoute.name, initialChildren: children);

  static const String name = 'VerifyCodeRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i19.VerifyCodeScreen();
    },
  );
}
