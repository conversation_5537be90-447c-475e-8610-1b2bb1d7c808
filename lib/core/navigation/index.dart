import 'package:auto_route/auto_route.dart';
import 'package:tony_clou_app/core/navigation/index.gr.dart';

@AutoRouterConfig(replaceInRouteName: 'Screen|Page,Route')
class AppRouter extends RootStackRouter {
  @override
  RouteType get defaultRouteType => RouteType.cupertino();

  @override
  List<AutoRoute> get routes => [
    // initial screen (redirector)
    AutoRoute(page: SplashRoute.page, initial: true, path: '/splash'),

    // authorization screens
    AutoRoute(page: AuthorizationRoute.page, path: '/authorization'),
    AutoRoute(page: VerifyCodeRoute.page, path: '/verifyCode'),
    AutoRoute(page: AlternativeSignInRoute.page, path: '/alternativeSignIn'),

    // registration screens
    AutoRoute(page: RegistationRoute.page, path: '/registration'),
    AutoRoute(page: RegistrationRouteStep2.page, path: '/registration2'),
    AutoRoute(page: RegistrationRouteStep3.page, path: '/registration3'),

    // main screen
    AutoRoute(page: MainRoute.page, path: '/main'),

    // main screen features
    AutoRoute(page: LocationsRoute.page, path: '/locations'),
    AutoRoute(page: AboutRoute.page, path: '/about'),
    AutoRoute(page: ContactsRoute.page, path: '/contacts'),

    AutoRoute(page: BalanceRoute.page, path: '/balance'),
    AutoRoute(page: ScanRoute.page, path: '/scan'),
    AutoRoute(page: AddCheckRoute.page, path: '/addCheck'),

    // user screens
    AutoRoute(page: UserRoute.page, path: '/user'),

    // admin screens
    AutoRoute(page: AdminRoute.page, path: '/admin'),
    AutoRoute(page: UserListRoute.page, path: '/users'),
    AutoRoute(page: ChecksListRoute.page, path: '/checks'),

    AutoRoute(page: GeneratedQRRoute.page, path: '/admin/generatedQR'),
  ];

  @override
  List<AutoRouteGuard> get guards => [];
}
