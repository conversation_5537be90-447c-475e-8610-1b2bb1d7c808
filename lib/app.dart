import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tony_clou_app/core/navigation/index.dart';
import 'package:tony_clou_app/core/settings.dart';
import 'package:tony_clou_app/features/authorization/presentation/bloc/index.dart';
import 'package:tony_clou_app/features/main/presentation/bloc/user/index.dart';
import 'package:tony_clou_app/shared/styles/themes.dart';

class App extends StatelessWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context) {
    CoreSettings().getSystemUIOverlaySettings(true);
    AppRouter appRouter = AppRouter();

    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => BlocUser()),
        BlocProvider(create: (_) => BlocAuthorization()),
      ],
      child: MaterialApp.router(
        restorationScopeId: 'app',
        scrollBehavior: CupertinoScrollBehavior(),
        debugShowCheckedModeBanner: false,

        theme: AppThemes.lightTheme,
        darkTheme: AppThemes.darkTheme,
        themeMode: ThemeMode.light,

        routerConfig: appRouter.config(
          navigatorObservers: () => [AutoRouteObserver()],
        ),
      ),
    );
  }
}
